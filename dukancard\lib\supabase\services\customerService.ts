import { SupabaseClient } from "@supabase/supabase-js";
import { TABLES, COLUMNS } from "../constants";
import { TablesInsert, TablesUpdate } from "../../../types/supabase";
import { getPublicUrlFromStorage, uploadFileToStorage, removeFileFromStorage } from "./sharedService";

/**
 * Fetches a user's customer profile.
 * @param supabase The Supabase client.
 * @param userId The ID of the user to fetch the profile for.
 * @returns An object containing the user's profile data or an error.
 */
export async function getUserProfile(supabase: SupabaseClient, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .select("*")
      .eq(COLUMNS.ID, userId)
      .single();

    if (error) {
      console.error(`Error fetching user profile: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching user profile: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Creates a new customer profile.
 * @param supabase The Supabase client.
 * @param profile The profile data to insert.
 * @returns An object containing the newly created profile data or an error.
 */
export async function createUserProfile(supabase: SupabaseClient, profile: TablesInsert<'customer_profiles'>) {
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .insert([profile])
      .select()
      .single();

    if (error) {
      console.error(`Error creating user profile: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data: Array.isArray(data) ? data[0] || null : data || null, error: null };
  } catch (err) {
    console.error(`Unexpected error creating user profile: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Checks if a customer profile exists for a given user ID.
 * @param supabase The Supabase client.
 * @param userId The ID of the user to check.
 * @returns An object containing a boolean indicating if the profile exists and an error if one occurred.
 */
export async function checkIfCustomerProfileExists(supabase: SupabaseClient, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .select(COLUMNS.ID)
      .eq(COLUMNS.ID, userId)
      .maybeSingle();

    if (error) {
      console.error(`Error checking existing profile: ${error.message}`);
      return { exists: false, error: "Database error checking profile." };
    }

    return { exists: !!data, error: null };
  } catch (err) {
    console.error(`Unexpected error checking profile: ${err}`);
    return { exists: false, error: "An unexpected error occurred." };
  }
}

/**
 * Updates a user's profile.
 * @param supabase The Supabase client.
 * @param userId The ID of the user to update.
 * @param updates The profile data to update.
 * @returns An object containing the updated profile data or an error.
 */
export async function updateUserProfile(supabase: SupabaseClient, userId: string, updates: TablesUpdate<'customer_profiles'>) {
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .update(updates)
      .eq(COLUMNS.ID, userId)
      .select()
      .single();

    if (error) {
      console.error(`Error updating user profile: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error updating user profile: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the locality, pincode, and city slug for a customer profile.
 * @param supabase The Supabase client.
 * @param userId The ID of the user.
 * @returns An object containing the location data or an error.
 */
export async function getCustomerProfileLocation(supabase: SupabaseClient, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .select(`${COLUMNS.LOCALITY_SLUG}, ${COLUMNS.PINCODE}, ${COLUMNS.CITY_SLUG}, ${COLUMNS.STATE_SLUG}`)
      .eq(COLUMNS.ID, userId)
      .single();

    if (error) {
      console.error(`Error fetching customer profile location: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching customer profile location: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches customer profiles by their IDs.
 * @param supabase The Supabase client.
 * @param userIds An array of user IDs.
 * @returns An object containing the customer profiles data or an error.
 */
export async function getCustomerProfilesByIds(supabase: SupabaseClient, userIds: string[]) {
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .select(`${COLUMNS.ID}, ${COLUMNS.NAME}, ${COLUMNS.EMAIL}, ${COLUMNS.AVATAR_URL}`)
      .in(COLUMNS.ID, userIds);

    if (error) {
      console.error(`Error fetching customer profiles by IDs: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching customer profiles by IDs: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches a public customer profile by ID.
 * @param supabase The Supabase client.
 * @param userId The ID of the user to fetch the profile for.
 * @returns An object containing the public customer profile data or an error.
 */
export async function getPublicCustomerProfileById(supabase: SupabaseClient, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES_PUBLIC)
      .select('*')
      .eq(COLUMNS.ID, userId)
      .maybeSingle();

    if (error) {
      console.error(`Error fetching public customer profile: ${error.message}`);
      return { data: null, error: `Failed to fetch customer profile: ${error.message}` };
    }

    return { data, error: null };
  } catch (e) {
    console.error(`Exception in getPublicCustomerProfileById: ${e}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches public customer profiles by their IDs.
 * @param supabase The Supabase client.
 * @param userIds An array of user IDs.
 * @returns An object containing the public customer profiles data or an error.
 */
export async function getPublicCustomerProfilesByIds(supabase: SupabaseClient, userIds: string[]) {
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES_PUBLIC)
      .select(`${COLUMNS.ID}, ${COLUMNS.NAME}, ${COLUMNS.AVATAR_URL}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}`)
      .in(COLUMNS.ID, userIds);

    if (error) {
      console.error(`Error fetching public customer profiles by IDs: ${error.message}`);
      return { data: null, error: `Failed to fetch customer profiles: ${error.message}` };
    }

    return { data, error: null };
  } catch (e) {
    console.error(`Exception in getPublicCustomerProfilesByIds: ${e}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}
