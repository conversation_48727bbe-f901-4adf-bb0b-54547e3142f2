import { createClient } from '@/utils/supabase/server';

import { NextRequest, NextResponse } from 'next/server';
import { TABLES, COLUMNS } from "@/lib/supabase/constants";
import { Database } from "@/types/supabase";
import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
import { getBusinessProfileIdOnly } from "@/lib/supabase/services/businessService";



export async function GET(request: NextRequest) {
  try {
    // Get the authenticated user
    const supabase = await createClient();
    const { user, error: authError } = await getAuthenticatedUser(supabase);

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const perPage = 8; // Optimized for 2-column grid (1x8, 2x4)
    const sortBy = searchParams.get('sort') || 'newest';
    const businessProfileId = searchParams.get('businessProfileId');

    if (!businessProfileId) {
      return NextResponse.json(
        { error: 'Business profile ID is required' },
        { status: 400 }
      );
    }

    // Verify the user has a business profile
    const { data: businessProfile, error: profileError } = await getBusinessProfileIdOnly(supabase, user.id);

    if (profileError || !businessProfile) {
      return NextResponse.json(
        { error: 'You do not have access to this business profile' },
        { status: 403 }
      );
    }

    // Verify the requested business profile exists
    const { data: requestedProfile, error: requestedProfileError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.ID)
      .eq(COLUMNS.ID, businessProfileId)
      .single();

    if (requestedProfileError || !requestedProfile) {
      return NextResponse.json(
        { error: 'Business profile not found' },
        { status: 404 }
      );
    }

    // Calculate pagination
    const from = (page - 1) * perPage;

    // Simple query for all sorting options (no name sorting)
    let baseQuery = supabase
      .from(TABLES.RATINGS_REVIEWS)
      .select(`
        ${COLUMNS.ID},
        ${COLUMNS.RATING},
        review_text,
        ${COLUMNS.CREATED_AT},
        ${COLUMNS.UPDATED_AT},
        ${COLUMNS.BUSINESS_PROFILE_ID},
        ${COLUMNS.USER_ID}
      `, { count: 'exact' })
      .eq(COLUMNS.BUSINESS_PROFILE_ID, businessProfileId)
      .neq(COLUMNS.USER_ID, businessProfileId);

    // Apply sorting
    switch (sortBy) {
      case "oldest":
        baseQuery = baseQuery.order(COLUMNS.CREATED_AT, { ascending: true });
        break;
      case "highest_rating":
        baseQuery = baseQuery.order("rating", { ascending: false });
        break;
      case "lowest_rating":
        baseQuery = baseQuery.order("rating", { ascending: true });
        break;
      case "newest":
      default:
        baseQuery = baseQuery.order(COLUMNS.CREATED_AT, { ascending: false });
        break;
    }


    // Get count first
    const { count: totalCount, error: countError } = await baseQuery;
    if (countError) {
      console.error('Error counting reviews:', countError);
      return NextResponse.json(
        { error: 'Failed to count reviews' },
        { status: 500 }
      );
    }

    // Get paginated data
    const { data: reviews, error: reviewsError } = await baseQuery.range(from, from + perPage - 1);
    if (reviewsError) {
      console.error('Error fetching reviews:', reviewsError);
      return NextResponse.json(
        { error: 'Failed to fetch reviews' },
        { status: 500 }
      );
    }

    let processedReviews: Array<{
      id: string;
      rating: number;
      review_text: string | null;
      created_at: string;
      updated_at: string | null;
      business_profile_id: string;
      user_id: string;
      reviewer_type: 'business' | 'customer';
      reviewer_name: string;
      reviewer_avatar: string | null;
      reviewer_slug: string | null;
    }> = [];

    type ReviewData = Database["public"]["Tables"]["ratings_reviews"]["Row"];

    if (reviews && reviews.length > 0) {
      // Get user IDs for profile lookup
      const userIds = reviews.map((review: ReviewData) => review.user_id);

      // Fetch both customer and business profiles
      const [businessProfiles, customerProfiles] = await Promise.all([
        supabase
          .from(TABLES.BUSINESS_PROFILES)
          .select('id, business_name, business_slug, logo_url')
          .in('id', userIds),
        supabase
          .from(TABLES.CUSTOMER_PROFILES)
          .select('id, name, avatar_url')
          .in('id', userIds)
      ]);

      // Define interfaces for profile data
      type BusinessProfileForReview = Pick<Database["public"]["Tables"]["business_profiles"]["Row"], "id" | "business_name" | "business_slug" | "logo_url">;

      type CustomerProfileForReview = Pick<Database["public"]["Tables"]["customer_profiles"]["Row"], "id" | "name" | "avatar_url">;

      // Create profile maps
      const businessProfilesMap = (businessProfiles.data || []).reduce((map: Record<string, BusinessProfileForReview>, profile: BusinessProfileForReview) => {
        map[profile.id] = profile;
        return map;
      }, {} as Record<string, BusinessProfileForReview>);

      const customerProfilesMap = (customerProfiles.data || []).reduce((map: Record<string, CustomerProfileForReview>, profile: CustomerProfileForReview) => {
        map[profile.id] = profile;
        return map;
      }, {} as Record<string, CustomerProfileForReview>);

      // Process reviews with profile information
      processedReviews = reviews.map((review: ReviewData) => {
        const userId = review.user_id;
        const isBusiness = !!businessProfilesMap[userId];

        if (isBusiness) {
          const businessProfile = businessProfilesMap[userId];
          return {
            ...review,
            reviewer_type: 'business' as const,
            reviewer_name: businessProfile?.business_name || 'Business User',
            reviewer_avatar: businessProfile?.logo_url,
            reviewer_slug: businessProfile?.business_slug
          };
        } else {
          const customerProfile = customerProfilesMap[userId];
          return {
            ...review,
            reviewer_type: 'customer' as const,
            reviewer_name: customerProfile?.name || 'Customer',
            reviewer_avatar: customerProfile?.avatar_url,
            reviewer_slug: null
          };
        }
      });


    }

    const totalPages = Math.ceil((totalCount || 0) / perPage);

    return NextResponse.json({
      reviews: processedReviews,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        perPage
      }
    });
  } catch (_error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
