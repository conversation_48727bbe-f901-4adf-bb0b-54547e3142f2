Files in dukancard-app project using Supabase (excluding service files):
Total files found: 192

app\(auth)\choose-role.tsx
  Line 9: import { createCustomerProfile } from '@/backend/supabase/services/customer/customerProfileService';

app\(auth)\complete-profile.tsx
  Line 1: import { cleanAddressData } from "@/backend/supabase/utils/addressValidation";
  Line 32: } from "@/src/config/supabase/services/sharedService";
  Line 36: } from "@/src/config/supabase/services/customerService";

app\(auth)\components\Step1AvatarName.tsx
  Line 10: import { openCameraForAvatar, openGalleryForAvatar } from "@/src/config/supabase/services/sharedService";

app\(auth)\login.tsx
  Line 13: import { sendEmailOTP, verifyEmailOTP, signInWithGoogleNative, signInWithMobilePassword } from "@/src/config/supabase/services/sharedService";

app\(auth)\_layout.tsx
  Line 2: import { signOutUser } from "@/src/config/supabase/services/sharedService";

app\(dashboard)\business\index.tsx
  Line 13: import { getSubscriptionsWithBusinessProfiles, getBusinessProfileLocation } from "@/src/config/supabase/services/businessService";
  Line 14: import { getCustomerProfileLocation } from "@/src/config/supabase/services/customerService";

app\(dashboard)\business\profile.tsx
  Line 46: import { getBusinessProfile } from "@/backend/supabase/services/common/profileService";
  Line 47: import { Tables } from "@dukancard-types/supabase";

app\(dashboard)\customer\components\CustomerMetricsOverview.tsx
  Line 8: import { getCustomerMetrics, CustomerMetrics } from '@/backend/supabase/services/common/metricsService';

app\(dashboard)\customer\favorites.tsx
  Line 18: import { getCurrentUser, fetchLikes as fetchLikesService, unlikeBusiness, LikeWithProfile, LikesResult } from "@/src/config/supabase/services/sharedService";
  Line 19: import { User } from '@supabase/supabase-js';

app\(dashboard)\customer\index.tsx
  Line 16: import { supabase } from '@/lib/supabase';
  Line 48: const { data: subscriptions } = await supabase
  Line 76: supabase
  Line 81: supabase

app\(dashboard)\customer\profile\components\AvatarUpload.tsx
  Line 6: import { openCameraForAvatar, openGalleryForAvatar, uploadAvatarImage } from "@/backend/supabase/services/storage/avatarUploadService";

app\(dashboard)\customer\profile.tsx
  Line 15: import { getCustomerMetrics } from "@/backend/supabase/services/common/metricsService";
  Line 16: import { getCustomerProfile } from "@/backend/supabase/services/common/profileService";

app\(onboarding)\address-information.tsx
  Line 23: import { getOnboardingData, saveOnboardingData } from '@/backend/supabase/services/common/onboardingService';

app\(onboarding)\business-details.tsx
  Line 17: import { getOnboardingData, saveOnboardingData } from '@/backend/supabase/services/common/onboardingService';

app\(onboarding)\card-information.tsx
  Line 16: } from "@/backend/supabase/services/common/onboardingService";

app\(onboarding)\plan-selection.tsx
  Line 5: import { completeBusinessOnboarding, saveOnboardingData } from '@/backend/supabase/services/common/onboardingService';

app\(onboarding)\_layout.tsx
  Line 6: import { supabase } from '@/lib/supabase';
  Line 19: await supabase.auth.signOut();

app\(tabs)\index.tsx
  Line 9: import { getUserDisplayName } from '@/lib/supabase';

app\business\[businessSlug].tsx
  Line 14: import { discoverBusinessForUser } from '@/backend/supabase/services/business/businessDiscovery';
  Line 16: import { Tables } from '@/src/types/supabase';

app\product\[productId].tsx
  Line 13: import { supabase } from "@/lib/supabase";
  Line 36: import { fetchCustomAd } from "@/backend/supabase/services/ads/adService";
  Line 124: const { supabase } = await import("@/lib/supabase");
  Line 126: const { data, error } = await supabase
  Line 184: const { data, error } = await supabase
  Line 220: const { data: subscriptionData, error: subscriptionError } = await supabase
  Line 259: const { data, error } = await supabase
  Line 313: const { data: validBusinesses, error: businessError } = await supabase
  Line 326: const { data, error } = await supabase

app\_layout.tsx
  Line 26: import { configureGoogleAuth } from "@/backend/supabase/services/auth/nativeGoogleAuth2025";
  Line 29: import { productionErrorLogger } from "@/backend/supabase/services/monitoring/productionErrorLogging";

backend\supabase\services\activities\activityService.ts
  Line 6: import { fetchCustomerActivities, fetchBusinessProfilesByIds, fetchBatchProfiles } from '@/src/config/supabase/services/customerService';
  Line 109: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 173: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 189: let query = supabase
  Line 322: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 333: let query = supabase
  Line 361: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 372: const { count, error } = await supabase

backend\supabase\services\ads\adService.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 12: const { count, error: tableCheckError } = await supabase
  Line 21: const { data: adData, error: adError } = await supabase.rpc(
  Line 56: const { data: customAd } = await supabase
  Line 76: const { data: globalAd } = await supabase

backend\supabase\services\auth\authService.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 6: const result = await supabase.auth.getUser();
  Line 16: await supabase.auth.signOut();
  Line 31: await supabase.auth.signOut();
  Line 42: return await supabase
  Line 50: const { data, error } = await supabase
  Line 59: const { data, error } = await supabase
  Line 68: return await supabase
  Line 76: return await supabase.auth.signOut();
  Line 80: return await supabase.auth.signInWithPassword({

backend\supabase\services\auth\emailOtpService.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 14: const { error } = await supabase.auth.signInWithOtp({
  Line 62: const { error } = await supabase.auth.verifyOtp({

backend\supabase\services\auth\googleAuthService.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 21: const { data, error } = await supabase.auth.signInWithOAuth({
  Line 74: const { error: sessionError } = await supabase.auth.setSession({
  Line 121: const { error } = await supabase.auth.signInWithOAuth({

backend\supabase\services\auth\mobileAuthService.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 6: return await supabase.auth.signInWithPassword({

backend\supabase\services\auth\nativeGoogleAuth2025.ts
  Line 2: import { supabase } from '@/lib/supabase';
  Line 71: webClientId, // MUST be Web Application client ID for Supabase
  Line 107: const { signInWithGoogle } = await import('@/backend/supabase/services/auth/googleAuthService');
  Line 147: const { data, error } = await supabase.auth.signInWithIdToken({
  Line 154: console.error('❌ Supabase sign-in error:', error);
  Line 198: console.error('4. Client ID not matching Supabase configuration');

backend\supabase\services\business\businessAnalyticsService.ts
  Line 1: import { supabase } from "@/lib/supabase";
  Line 23: const { data, error } = await supabase
  Line 33: const { count: total_products, error: productsError } = await supabase
  Line 49: await supabase.rpc("get_monthly_unique_visits", {
  Line 79: const { data: profile, error: profileError } = await supabase
  Line 110: const { data: trend7Result, error: trend7Error } = await supabase.rpc(
  Line 124: const { data: trend30Result, error: trend30Error } = await supabase.rpc(
  Line 139: await supabase.rpc("get_hourly_unique_visit_trend", {
  Line 151: await supabase.rpc("get_monthly_unique_visits", {
  Line 162: await supabase.rpc("get_monthly_unique_visits", {
  Line 178: await supabase.rpc("get_available_years_for_monthly_metrics", {
  Line 196: await supabase.rpc("get_monthly_unique_visit_trend", {
  Line 226: const { data: totalCount, error: totalError } = await supabase.rpc(

backend\supabase\services\business\businessCardDataService.ts
  Line 1: import { supabase } from "@/lib/supabase";
  Line 3: import { Tables } from "../../../../src/types/supabase";
  Line 23: const { data, error } = await supabase
  Line 56: const { data: reviewsData, error } = await supabase
  Line 87: supabase
  Line 91: supabase
  Line 163: const { data: businessData, error: businessError } = await supabase
  Line 179: const { data: subscriptionData, error: subscriptionError } = await supabase
  Line 236: const { data, error } = await supabase
  Line 320: let countQuery = supabase
  Line 347: let dataQuery = supabase
  Line 427: const { count: totalCount, error: countError } = await supabase
  Line 448: let reviewsQuery = supabase
  Line 498: supabase
  Line 502: supabase

backend\supabase\services\business\businessCardService.ts
  Line 1: import { supabase } from "../../../../src/config/supabase";
  Line 3: import { Json } from "@/src/types/supabase";
  Line 18: } = await supabase.auth.getUser();
  Line 25: const { data, error } = await supabase
  Line 45: console.error("Supabase Fetch Error:", error);
  Line 56: const { error: updateError } = await supabase
  Line 100: } = await supabase.auth.getUser();
  Line 108: const { data: existingProfile, error: profileError } = await supabase
  Line 174: const { data: updatedProfile, error: updateError } = await supabase
  Line 191: console.error("Supabase Update Error:", updateError);
  Line 211: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 253: } = await supabase.auth.getUser();
  Line 260: const { data: updatedProfile, error: updateError } = await supabase
  Line 304: } = await supabase.auth.getUser();
  Line 311: const { data: existingProfile, error: profileError } = await supabase
  Line 322: const { data: updatedProfile, error: updateError } = await supabase
  Line 347: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 376: } = await supabase.auth.getUser();
  Line 383: const { data: updatedProfile, error: updateError } = await supabase
  Line 415: } = await supabase.auth.getUser();
  Line 422: const { data: updatedProfile, error: updateError } = await supabase
  Line 453: } = await supabase.auth.getUser();
  Line 460: const { data: updatedProfile, error: updateError } = await supabase
  Line 490: } = await supabase.auth.getUser();
  Line 501: const { data: currentProfile, error: profileError } = await supabase
  Line 534: const { data: updatedProfile, error: updateError } = await supabase
  Line 576: } = await supabase.auth.getUser();
  Line 582: const { data, error } = await supabase
  Line 619: } = await supabase.auth.getUser();
  Line 625: const { data, error } = await supabase
  Line 654: } = await supabase.auth.getUser();
  Line 660: const { data, error } = await supabase
  Line 690: } = await supabase.auth.getUser();
  Line 696: const { data, error } = await supabase
  Line 725: } = await supabase.auth.getUser();
  Line 731: const { data, error } = await supabase
  Line 759: } = await supabase.auth.getUser();
  Line 765: const { data, error } = await supabase
  Line 794: } = await supabase.auth.getUser();
  Line 800: const { data, error } = await supabase

backend\supabase\services\business\businessDiscovery.ts
  Line 1: import { supabase } from "@/lib/supabase";
  Line 2: import { Tables, Json } from "@/src/types/supabase";
  Line 33: const { data: businessProfile, error } = await supabase
  Line 103: const { data: subscriptionData, error: subscriptionError } = await supabase
  Line 142: const { data, error } = await supabase
  Line 174: const { data, error } = await supabase

backend\supabase\services\business\businessInteractions.ts
  Line 6: import { supabase } from '@/lib/supabase';
  Line 32: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 37: supabase
  Line 41: supabase
  Line 45: supabase
  Line 72: supabase
  Line 78: supabase
  Line 84: supabase
  Line 90: supabase
  Line 94: supabase
  Line 98: supabase
  Line 137: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 148: const { data: existingLike, error: checkError } = await supabase
  Line 161: const { error: deleteError } = await supabase
  Line 174: const { error: insertError } = await supabase
  Line 205: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 216: const { data: existingSubscription, error: checkError } = await supabase
  Line 229: const { error: deleteError } = await supabase
  Line 242: const { error: insertError } = await supabase
  Line 271: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 277: const { data: business, error } = await supabase
  Line 300: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 311: const { data: existingReview, error: checkError } = await supabase
  Line 324: const { error: updateError } = await supabase
  Line 341: const { error: insertError } = await supabase

backend\supabase\services\business\businessOnboardingService.ts
  Line 13: import { supabase } from '@/lib/supabase';
  Line 81: const { data: result, error } = await supabase.rpc('create_business_profile_atomic', {
  Line 111: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 137: const { data: existingBusiness, error: slugCheckError } = await supabase
  Line 224: const { data: existingBusiness, error } = await supabase
  Line 247: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 253: const { data: profile, error } = await supabase
  Line 317: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 346: const { data: profile, error } = await supabase

backend\supabase\services\business\businessPostService.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 9: return await supabase
  Line 20: return await supabase
  Line 31: return await supabase
  Line 43: return await supabase
  Line 55: return await supabase
  Line 65: return await supabase
  Line 89: return await supabase

backend\supabase\services\business\businessProductsService.ts
  Line 1: import { supabase } from "@/src/config/supabase";
  Line 2: import { Tables, TablesInsert, TablesUpdate } from "@dukancard-types/supabase";
  Line 63: } = await supabase.auth.getUser();
  Line 69: let query = supabase
  Line 156: } = await supabase.auth.getUser();
  Line 162: let query = supabase
  Line 239: } = await supabase.auth.getUser();
  Line 257: const { data: insertedProduct, error: insertError } = await supabase
  Line 291: const { error: uploadError } = await supabase.storage
  Line 302: const { data: urlData } = supabase.storage
  Line 309: const { data: updatedProduct, error: updateError } = await supabase
  Line 365: } = await supabase.auth.getUser();
  Line 393: const { error: uploadError } = await supabase.storage
  Line 404: const { data: urlData } = supabase.storage
  Line 423: const { data: updatedProduct, error: updateError } = await supabase
  Line 478: } = await supabase.auth.getUser();
  Line 484: const { data: product, error: fetchError } = await supabase
  Line 505: const { error: deleteImagesError } = await supabase.storage
  Line 515: const { error: deleteError } = await supabase

backend\supabase\services\business\businessProfileService.ts
  Line 13: import { supabase } from "@/lib/supabase";
  Line 14: import { Tables, TablesInsert, TablesUpdate } from "../../../../src/types/supabase";
  Line 36: } = await supabase.auth.getUser();
  Line 42: const { data: profile, error } = await supabase
  Line 97: } = await supabase.auth.getUser();
  Line 134: const { data: result, error: rpcError } = await supabase.rpc('create_business_profile_atomic', {
  Line 174: } = await supabase.auth.getUser();
  Line 181: const { data: profile, error } = await supabase
  Line 214: const { data: profile, error } = await supabase
  Line 247: const { data: profiles, error } = await supabase
  Line 274: const { data: existing, error } = await supabase
  Line 304: } = await supabase.auth.getUser();
  Line 311: const { error } = await supabase

backend\supabase\services\business\businessSocialService.ts
  Line 1: import { supabase } from "@/lib/supabase";
  Line 2: import { Tables } from "@dukancard-types/supabase";
  Line 60: const { count: totalCount, error: countError } = await supabase
  Line 80: const { data: likes, error: likesError } = await supabase
  Line 104: supabase
  Line 108: supabase
  Line 173: let query = supabase
  Line 198: let countQuery = supabase
  Line 333: const { count: totalCount, error: countError } = await supabase
  Line 352: const { data: subscriptions, error: subsError } = await supabase
  Line 377: supabase
  Line 381: supabase
  Line 475: let query = supabase

backend\supabase\services\business\planLimitService.ts
  Line 1: import { supabase } from "@/src/config/supabase";
  Line 27: } = await supabase.auth.getUser();
  Line 34: const { data: subscriptionData, error: subscriptionError } = await supabase
  Line 85: const { count: availableCount, error: availableCountError } = await supabase
  Line 96: const { count: totalCount, error: totalCountError } = await supabase
  Line 159: } = await supabase.auth.getUser();
  Line 165: const { data: product, error: productError } = await supabase

backend\supabase\services\business\utils\slugUtils.ts
  Line 1: import { supabase } from "../../../../../src/config/supabase";
  Line 17: const { data, error } = await supabase
  Line 37: const { data, error } = await supabase

backend\supabase\services\business\utils\subscriptionChecker.ts
  Line 1: import { supabase } from "../../../../../src/config/supabase";
  Line 7: const { data, error } = await supabase

backend\supabase\services\business\variantService.ts
  Line 1: import { supabase } from "@/src/config/supabase";
  Line 2: import { Tables, TablesInsert, TablesUpdate } from "@/src/types/supabase";
  Line 24: } = await supabase.auth.getUser();
  Line 30: const { data: product, error: productError } = await supabase
  Line 53: const { data: insertedVariant, error: insertError } = await supabase
  Line 79: const { error: uploadError } = await supabase.storage
  Line 91: const { data: urlData } = supabase.storage
  Line 109: const { data: updatedVariant, error: updateError } = await supabase
  Line 145: } = await supabase.auth.getUser();
  Line 151: const { data: variant, error: variantError } = await supabase
  Line 193: const { error: uploadError } = await supabase.storage
  Line 205: const { data: urlData } = supabase.storage
  Line 224: const { data: updatedVariant, error: updateError } = await supabase
  Line 252: } = await supabase.auth.getUser();
  Line 258: const { data: variant, error: variantError } = await supabase
  Line 286: const { error: deleteError } = await supabase.storage
  Line 298: const { error: deleteError } = await supabase
  Line 324: } = await supabase.auth.getUser();
  Line 330: const { data: product, error: productError } = await supabase
  Line 342: const { data: variants, error: variantsError } = await supabase

backend\supabase\services\common\metricsService.ts
  Line 6: import { supabase } from '@/lib/supabase';
  Line 27: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 39: supabase
  Line 45: supabase
  Line 51: supabase
  Line 105: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 111: const { count, error } = await supabase

backend\supabase\services\common\onboardingService.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 2: import { completeBusinessOnboarding as completeOnboarding } from '@/backend/supabase/services/business/businessOnboardingService';
  Line 112: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 118: const { data: existingProfile, error } = await supabase
  Line 144: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 166: const { data: existingProfile } = await supabase
  Line 238: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 243: const { data: profile, error } = await supabase

backend\supabase\services\common\profileCheckService.ts
  Line 13: import { supabase } from '@/lib/supabase';
  Line 34: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 43: const { data: profile, error } = await supabase
  Line 88: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 96: supabase
  Line 101: supabase
  Line 148: const { data: profile, error } = await supabase
  Line 187: const { data: profiles, error } = await supabase

backend\supabase\services\common\profileService.ts
  Line 1: import { supabase } from "@/lib/supabase";
  Line 48: } = await supabase.auth.getUser();
  Line 56: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 78: const { data: result, error } = await supabase
  Line 111: } = await supabase.auth.getUser();
  Line 126: const { data: result, error } = await supabase
  Line 161: } = await supabase.auth.getUser();
  Line 168: const { data: profile, error } = await supabase
  Line 198: } = await supabase.auth.getUser();
  Line 205: const { data: profile, error } = await supabase
  Line 237: } = await supabase.auth.getUser();
  Line 243: const { data: result, error } = await supabase
  Line 273: const { data: pincodeData, error } = await supabase
  Line 313: const { data: pincodeData, error } = await supabase
  Line 354: } = await supabase.auth.getUser();
  Line 360: const { data: profile, error } = await supabase

backend\supabase\services\common\qrScanService.ts
  Line 2: import { validateBusinessSlug } from '@/backend/supabase/utils/businessSlugValidation';
  Line 3: import { discoverBusinessForUser } from '@/backend/supabase/services/business/businessDiscovery';

backend\supabase\services\common\settingsService.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 21: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 34: const { data: existingUser, error: checkError } = await supabase
  Line 53: const { error: otpError } = await supabase.auth.signInWithOtp({
  Line 73: const { error: updateError } = await supabase.auth.updateUser({
  Line 94: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 101: const { error: verifyError } = await supabase.auth.verifyOtp({
  Line 113: const { error: updateError } = await supabase.auth.updateUser({
  Line 134: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 146: const { data: existingUser, error: checkError } = await supabase
  Line 163: const { error: otpError } = await supabase.auth.signInWithOtp({
  Line 191: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 198: const { error: verifyError } = await supabase.auth.verifyOtp({
  Line 210: const { error: updateError } = await supabase.auth.updateUser({
  Line 231: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 264: const { error: signInError } = await supabase.auth.signInWithPassword({
  Line 275: const { error: updateError } = await supabase.auth.updateUser({
  Line 296: const { error } = await supabase.auth.resetPasswordForEmail(email, {

backend\supabase\services\common\userRoleStatusService.ts
  Line 13: import { supabase } from '@/lib/supabase';
  Line 42: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 50: supabase
  Line 55: supabase

backend\supabase\services\customer\batchProfileService.ts
  Line 13: import { supabase } from '@/lib/supabase';
  Line 72: const { data: customerProfiles, error: customerError } = await supabase
  Line 87: const { data: businessProfiles, error: businessError } = await supabase
  Line 118: const { data: profiles, error } = await supabase
  Line 146: const { data: profiles, error } = await supabase

backend\supabase\services\customer\customerPostService.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 5: return await supabase
  Line 13: return await supabase
  Line 21: return await supabase
  Line 30: return await supabase
  Line 39: return await supabase
  Line 46: return await supabase

backend\supabase\services\customer\customerProfileService.ts
  Line 1: import { supabase } from "@/lib/supabase";
  Line 2: import { TABLES, COLUMNS } from "@/src/config/supabase/constants";
  Line 3: import { Database, Tables, TablesInsert, TablesUpdate } from "@dukancard-types/supabase";
  Line 18: return await supabase.auth.updateUser({
  Line 31: return await supabase
  Line 46: const { error: updateError } = await supabase
  Line 56: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 64: return await supabase.auth.updateUser({
  Line 71: return await supabase
  Line 80: const { data, error } = await supabase
  Line 102: const { data, error } = await supabase
  Line 115: return await supabase.from(TABLES.CUSTOMER_PROFILES).insert<TablesInsert<"customer_profiles">>({
  Line 125: return await supabase
  Line 151: const { data, error } = await supabase
  Line 177: return await supabase.from("customer_profiles").select("*").in("id", userIds);
  Line 181: return await supabase.from("customer_profiles").delete().eq("id", userId);
  Line 192: } = await supabase.auth.getUser();

backend\supabase\services\gallery\galleryService.ts
  Line 1: import { supabase } from '@/src/config/supabase';
  Line 9: const { data: profileData, error } = await supabase

backend\supabase\services\location\locationService.ts
  Line 1: import { supabase } from "@/lib/supabase";
  Line 4: import { Tables } from "@dukancard-types/supabase";
  Line 5: import { QueryData } from "@supabase/supabase-js";
  Line 9: const query = supabase
  Line 89: const query = supabase
  Line 101: console.error("Error fetching coordinates from Supabase:", error);
  Line 223: const { data: result, error } = await supabase.rpc(

backend\supabase\services\posts\postInteractions.ts
  Line 6: import { supabase } from '@/lib/supabase';
  Line 28: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 44: const { error: insertError } = await supabase
  Line 90: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 106: const { error: deleteError } = await supabase
  Line 145: const { data: { user } } = await supabase.auth.getUser();
  Line 153: const { count: likeCount } = await supabase
  Line 161: const { data: userLike } = await supabase

backend\supabase\services\posts\postService.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 5: return await supabase

backend\supabase\services\posts\socialService.ts
  Line 6: import { supabase } from "@/lib/supabase";
  Line 7: import { Tables } from "@dukancard-types/supabase";
  Line 86: let query = supabase
  Line 153: const { error } = await supabase
  Line 183: let query = supabase
  Line 202: let countQuery = supabase
  Line 281: const { error } = await supabase.from("likes").delete().eq("id", likeId);
  Line 312: let query = supabase
  Line 401: const { error } = await supabase
  Line 424: const { error } = await supabase
  Line 455: supabase
  Line 459: supabase
  Line 463: supabase

backend\supabase\services\posts\unifiedFeedService.ts
  Line 1: import { supabase } from "@/lib/supabase";
  Line 2: import { Tables } from "../../../../src/types/supabase";
  Line 3: import { PostgrestResponse } from "@supabase/supabase-js";
  Line 18: return await supabase
  Line 27: return await supabase
  Line 37: return await supabase

backend\supabase\services\products\addProduct.ts
  Line 3: import { getCurrentUser } from "@/src/config/supabase/services/sharedService";
  Line 4: import { insertProductServiceData, deleteProductServiceById, updateProductServiceData } from "@/src/config/supabase/services/businessService";
  Line 5: import { TABLES, COLUMNS } from "@/src/config/supabase/constants";
  Line 6: import { Tables } from "@/src/types/supabase";

backend\supabase\services\products\imageHandlers.ts
  Line 1: import { handleMultipleImageUpload } from "@/src/config/supabase/services/businessService";

backend\supabase\services\products\productService.ts
  Line 1: import { supabase } from "@/lib/supabase";
  Line 2: import { Tables, TablesInsert, TablesUpdate } from "../../../../src/types/supabase";
  Line 10: return await supabase
  Line 32: return await supabase
  Line 56: return await supabase
  Line 78: return await supabase
  Line 100: return await supabase

backend\supabase\services\products\updateProduct.ts
  Line 3: import { getCurrentUser } from "@/src/config/supabase/services/sharedService";
  Line 4: import { fetchProductServiceById, updateProductServiceData } from "@/src/config/supabase/services/businessService";
  Line 5: import { TABLES, COLUMNS } from "@/src/config/supabase/constants";
  Line 6: import { Tables } from "@/src/types/supabase";

backend\supabase\services\realtime\realtimeService.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 2: import { RealtimeChannel } from '@supabase/supabase-js';
  Line 47: const channel = supabase

backend\supabase\services\storage\avatarUploadService.ts
  Line 2: import { supabase } from "@/lib/supabase";
  Line 6: } from "@/backend/supabase/services/storage/storageService";
  Line 7: import { getCustomerAvatarPath } from "@/backend/supabase/utils/storage-paths";
  Line 296: const { error } = await supabase.storage
  Line 311: const { data: publicUrlData } = supabase.storage
  Line 347: const { error } = await supabase

backend\supabase\services\storage\businessPostImageUploadService.ts
  Line 6: import { supabase } from '@/lib/supabase';
  Line 8: import { getBusinessPostImagePath } from '@/backend/supabase/utils/storage-paths';
  Line 27: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 81: const { error: uploadError } = await supabase.storage
  Line 97: const { data: urlData } = supabase.storage
  Line 128: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 134: const { data: profile, error: profileError } = await supabase

backend\supabase\services\storage\customerPostImageUploadService.ts
  Line 6: import { supabase } from '@/lib/supabase';
  Line 8: import { getCustomerPostImagePath } from '@/backend/supabase/utils/storage-paths';
  Line 27: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 81: const { error: uploadError } = await supabase.storage
  Line 97: const { data: urlData } = supabase.storage
  Line 128: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 134: const { data: profile, error: profileError } = await supabase

backend\supabase\services\storage\imageUploadService.ts
  Line 8: import { supabase } from '@/lib/supabase';
  Line 273: const { data, error } = await supabase.storage
  Line 281: console.error('Supabase upload error:', error);
  Line 286: const { data: urlData } = supabase.storage

backend\supabase\services\storage\storageService.ts
  Line 14: import { supabase } from "@/lib/supabase";
  Line 15: import { getCustomerAvatarPath } from "@/backend/supabase/utils/storage-paths";
  Line 88: } = await supabase.auth.getUser();
  Line 99: const { data, error } = await supabase.storage
  Line 112: const { data: publicUrlData } = supabase.storage
  Line 142: } = await supabase.auth.getUser();
  Line 149: const { error } = await supabase.storage.from(bucket).remove([filePath]);
  Line 183: const { data: publicUrlData } = supabase.storage
  Line 212: const { data, error } = await supabase.storage
  Line 255: } = await supabase.auth.getUser();
  Line 266: const { data, error } = await supabase.storage
  Line 302: } = await supabase.auth.getUser();
  Line 322: await supabase.storage.listBuckets();
  Line 349: "[STORAGE_SERVICE] About to call supabase.storage.upload with:",
  Line 359: const { data, error } = await supabase.storage
  Line 384: const { data: publicUrlData } = supabase.storage

backend\supabase\utils\businessSlugValidation.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 48: const { data: businessProfile, error } = await supabase

backend\supabase\utils\index.ts
  Line 38: export { handleSupabaseError, isAuthError, isDatabaseError, SupabaseError } from './supabaseErrorHandler';

backend\supabase\utils\supabaseErrorHandler.ts
  Line 1: import { AuthError } from "@supabase/supabase-js";
  Line 80: export function handleSupabaseError(error: AuthError | Error | null): string {
  Line 142: export type SupabaseError = AuthError | Error;

backend\types\activities.ts
  Line 6: import { Tables } from "@dukancard-types/supabase";

backend\types\posts.ts
  Line 6: import { Tables } from "@dukancard-types/supabase";

backend\types\products.ts
  Line 6: import { Tables } from "@dukancard-types/supabase";

context-engineering-intro-main\PRPs\comprehensive_testing_plan.md
  Line 84: - file: dukancard-app/backend/supabase/services/index.ts
  Line 112: │   ├── supabase/services/  # 10+ service modules (auth, business, customer, etc.)
  Line 159: │   └── supabase/services/
  Line 259: - Configure Supabase client mocking
  Line 296: CREATE backend/supabase/services/**/*.test.ts:
  Line 471: import { createClient } from "@supabase/supabase-js";
  Line 474: jest.mock("@supabase/supabase-js");
  Line 475: const mockSupabase = createClient as jest.MockedFunction<typeof createClient>;
  Line 496: mockSupabase.mockReturnValue(mockClient);
  Line 664: - Supabase Client: Comprehensive mocking for all database operations
  Line 705: npm run test:api              # API integration tests with mocked Supabase
  Line 939: - Specific Supabase mocking strategies for complex real-time features

dependency-graph.json
  Line 21: "module": "@/lib/supabase",
  Line 31: "resolved": "lib/supabase.ts",
  Line 85: "source": "lib/supabase.ts",
  Line 89: "module": "../src/config/supabase",
  Line 96: "resolved": "src/config/supabase.ts",
  Line 144: "source": "src/config/supabase.ts",
  Line 165: "lib/supabase.ts",
  Line 181: "src/config/supabase.ts",
  Line 185: "lib/config/supabase.ts",
  Line 1437: "module": "@/lib/supabase",
  Line 1447: "resolved": "lib/supabase.ts",
  Line 1734: "module": "@/lib/supabase",
  Line 1744: "resolved": "lib/supabase.ts",
  Line 2539: "module": "../supabase",
  Line 2546: "resolved": "lib/supabase.ts",
  Line 2611: "module": "@/lib/supabase",
  Line 2621: "resolved": "lib/supabase.ts",
  Line 2682: "module": "@/lib/supabase",
  Line 2692: "resolved": "lib/supabase.ts",
  Line 3437: "module": "@/lib/supabase",
  Line 3447: "resolved": "lib/supabase.ts",
  Line 3627: "module": "@/lib/supabase",
  Line 3637: "resolved": "lib/supabase.ts",
  Line 4351: "module": "@/lib/supabase",
  Line 4361: "resolved": "lib/supabase.ts",
  Line 4459: "module": "@/lib/supabase",
  Line 4469: "resolved": "lib/supabase.ts",
  Line 4583: "module": "@/lib/supabase",
  Line 4593: "resolved": "lib/supabase.ts",
  Line 4763: "module": "@/lib/supabase",
  Line 4773: "resolved": "lib/supabase.ts",
  Line 4906: "module": "@/lib/supabase",
  Line 4916: "resolved": "lib/supabase.ts",
  Line 4970: "module": "@/lib/supabase",
  Line 4980: "resolved": "lib/supabase.ts",
  Line 5002: "module": "@/lib/supabase",
  Line 5012: "resolved": "lib/supabase.ts",
  Line 5130: "module": "@/lib/supabase",
  Line 5140: "resolved": "lib/supabase.ts",
  Line 5259: "module": "@/lib/supabase",
  Line 5269: "resolved": "lib/supabase.ts",
  Line 5349: "module": "@/lib/supabase",
  Line 5359: "resolved": "lib/supabase.ts",
  Line 5479: "module": "@/lib/supabase",
  Line 5489: "resolved": "lib/supabase.ts",
  Line 5553: "module": "@/lib/supabase",
  Line 5563: "resolved": "lib/supabase.ts",
  Line 6224: "module": "@/lib/supabase",
  Line 6234: "resolved": "lib/supabase.ts",
  Line 9810: "module": "@/src/config/supabase",
  Line 9820: "resolved": "src/config/supabase.ts",
  Line 10292: "module": "../config/supabase",
  Line 10299: "resolved": "src/config/supabase.ts",
  Line 10625: "module": "../../../config/supabase",
  Line 10632: "resolved": "src/config/supabase.ts",
  Line 10688: "module": "../../config/supabase",
  Line 10695: "resolved": "src/config/supabase.ts",
  Line 10750: "module": "../../config/supabase",
  Line 10757: "resolved": "src/config/supabase.ts",
  Line 10828: "module": "../../../config/supabase",
  Line 10835: "resolved": "src/config/supabase.ts",
  Line 10855: "module": "../../config/supabase",
  Line 10862: "resolved": "src/config/supabase.ts",
  Line 11462: "module": "@/lib/supabase",
  Line 11472: "resolved": "lib/supabase.ts",
  Line 12054: "module": "@/lib/supabase",
  Line 12064: "resolved": "lib/supabase.ts",
  Line 13432: "module": "@/lib/supabase",
  Line 13442: "resolved": "lib/supabase.ts",
  Line 13757: "module": "@/lib/supabase",
  Line 13767: "resolved": "lib/supabase.ts",
  Line 14455: "module": "@/lib/supabase",
  Line 14465: "resolved": "lib/supabase.ts",
  Line 15859: "module": "@/lib/supabase",
  Line 15869: "resolved": "lib/supabase.ts",
  Line 17555: "module": "@/lib/supabase",
  Line 17565: "resolved": "lib/supabase.ts",
  Line 18084: "module": "@/lib/supabase",
  Line 18094: "resolved": "lib/supabase.ts",
  Line 18114: "module": "@/lib/supabase",
  Line 18124: "resolved": "lib/supabase.ts",
  Line 18644: "module": "../supabase",
  Line 18651: "resolved": "lib/supabase.ts",
  Line 18710: "source": "lib/config/supabase.ts",
  Line 20338: "module": "@/lib/supabase",
  Line 20348: "resolved": "lib/supabase.ts",
  Line 21771: "module": "@/lib/supabase",
  Line 21781: "resolved": "lib/supabase.ts",

docs\auth-docs\BUILD_GUIDE.md
  Line 96: Error: Supabase configuration missing

docs\auth-docs\GOOGLE_OAUTH_SETUP.md
  Line 152: - Web Client ID: Used by Supabase for server-side verification
  Line 199: - [Supabase Auth with Google](https://supabase.com/docs/guides/auth/social-login/auth-google)

docs\auth-docs\KEYSTORE_CREDENTIALS.md
  Line 103: ## 🗄️ Supabase Configuration
  Line 107: Supabase URL: https://rnjolcoecogzgglnblqn.supabase.co
  Line 108: Supabase Anon Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o
  Line 109: Supabase Service Role Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzA1MjA1NiwiZXhwIjoyMDU4NjI4MDU2fQ.HGJ--VU4KdlcrT9Dsa2rWUPNtL_bYHEl6YfczRK8I0E

docs\brief-app.md
  Line 123: - **Backend:** Supabase (Database, Authentication, and other backend services)
  Line 124: - **Database:** Supabase (PostgreSQL)
  Line 125: - **Hosting/Infrastructure:** Cloud Supabase (Backend), Mobile App Stores (Frontend Distribution)
  Line 146: - **Budget:** Self-funded, with limited financial resources. Currently utilizing a **Redis free plan** and a **Supabase Pro plan**. Operational costs are a significant consideration, and careful management is required.
  Line 156: - **Supabase Scalability:** Supabase's Pro plan will adequately support initial user growth and transaction volumes without significant performance bottlenecks or unexpected costs.
  Line 167: - **Supabase Cost Escalation:** While currently on a Pro plan, unexpected increases in usage or changes in Supabase pricing could lead to unsustainable costs.
  Line 186: - **Scalability Beyond Supabase:** At what point will the current Supabase Pro plan become a bottleneck, and what are the alternative backend solutions or architectural changes required for significant scaling?
  Line 206: (To be populated with relevant links and documents, e.g., links to Supabase documentation, Razorpay API docs, etc.)

docs\brownfield-architecture-app.md
  Line 31: | Backend | Supabase | ^2.49.8 | Database, Auth, and other backend services |
  Line 89: | Supabase | Backend Platform | SDK | `lib/supabase.ts` |

jest.config.js
  Line 43: "backend/supabase/services/**/*.{ts,tsx}",

jest.setup.js
  Line 92: supabaseUrl: "mock-supabase-url",
  Line 93: supabaseAnonKey: "mock-supabase-anon-key",
  Line 300: const mockSupabase = {
  Line 323: jest.mock("@supabase/supabase-js", () => ({
  Line 324: createClient: jest.fn(() => mockSupabase),
  Line 691: Object.keys(mockSupabase).forEach((key) => {
  Line 693: typeof mockSupabase[key] === "function" &&
  Line 694: mockSupabase[key].mockClear
  Line 696: mockSupabase[key].mockClear();
  Line 699: Object.keys(mockSupabase.auth).forEach((key) => {
  Line 701: typeof mockSupabase.auth[key] === "function" &&
  Line 702: mockSupabase.auth[key].mockClear
  Line 704: mockSupabase.auth[key].mockClear();
  Line 709: mockSupabase.from.mockImplementation(() => mockSupabase.from);
  Line 710: mockSupabase.auth.getSession.mockResolvedValue({
  Line 714: mockSupabase.auth.getUser.mockResolvedValue({
  Line 718: mockSupabase.auth.onAuthStateChange.mockReturnValue({
  Line 721: mockSupabase.rpc.mockResolvedValue({ data: null, error: null });

lib\actions\business\onboarding.ts
  Line 1: import { completeBusinessOnboarding, BusinessOnboardingData, ServiceResult } from '@/backend/supabase/services/business/businessOnboardingService';

lib\actions\businessPosts.ts
  Line 6: import { supabase } from '@/lib/supabase';
  Line 7: import { checkBusinessProfile } from '@/backend/supabase/services/storage/businessPostImageUploadService';
  Line 9: import { BusinessPostService } from '@/backend/supabase/services/business/businessPostService';
  Line 25: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 121: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 175: const { data: { user }, error: userError } = await supabase.auth.getUser();

lib\actions\customer\profileActions.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 18: } from '@/backend/supabase/utils/profileValidation';
  Line 19: import { CustomerProfileService } from '@/backend/supabase/services/customer/customerProfileService';
  Line 73: } = await supabase.auth.getUser();
  Line 121: } = await supabase.auth.getUser();
  Line 170: } = await supabase.auth.getUser();
  Line 219: } = await supabase.auth.getUser();
  Line 321: } = await supabase.auth.getUser();

lib\actions\customer\settingsSchema.ts
  Line 1: import { validateEmail, validateIndianMobile, validateRequired, validatePincode } from '@/backend/supabase/utils/validation';

lib\actions\customerPosts.ts
  Line 6: import { supabase } from '@/lib/supabase';
  Line 7: import { checkCustomerAddress } from '@/backend/supabase/services/storage/customerPostImageUploadService';
  Line 9: import { CustomerPostService } from '@/backend/supabase/services/customer/customerPostService';
  Line 26: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 68: const { data: customerProfile, error: profileError } = await supabase
  Line 127: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 190: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 265: const { data: { user }, error: userError } = await supabase.auth.getUser();

lib\actions\posts\fetchSinglePost.ts
  Line 1: import { supabase } from "@/lib/supabase";
  Line 3: import { PostService } from '@/backend/supabase/services/posts/postService';

lib\actions\posts\unifiedFeed.ts
  Line 1: import { supabase } from "@/lib/supabase";
  Line 9: import { UnifiedFeedService } from "@/backend/supabase/services/posts/unifiedFeedService";
  Line 67: } = await supabase.auth.getUser();
  Line 70: let query = supabase.from("unified_posts").select("*", { count: "exact" });

lib\actions\products.ts
  Line 6: import { supabase } from "@/lib/supabase";
  Line 7: import { ProductService } from "@/backend/supabase/services/products/productService";
  Line 8: import { Tables } from "@dukancard-types/supabase";
  Line 39: } = await supabase.auth.getUser();
  Line 98: } = await supabase.auth.getUser();
  Line 150: } = await supabase.auth.getUser();
  Line 208: } = await supabase.auth.getUser();

lib\auth\customerAuth.ts
  Line 2: import { supabase } from "../supabase";
  Line 8: } from "@/backend/supabase/utils/addressValidation";
  Line 9: import { AuthService } from "@/backend/supabase/services/auth/authService";
  Line 10: import { Tables } from "@dukancard-types/supabase";
  Line 47: await supabase.auth.signOut();
  Line 72: await supabase.auth.signOut();

lib\auth\profileValidationMiddleware.ts
  Line 3: import { supabase } from "../supabase";

lib\config\supabase.ts
  Line 6: import { SUPABASE_CONFIG as HARDCODED_CONFIG } from '../../src/config/publicKeys';
  Line 9: export const SUPABASE_CONFIG = HARDCODED_CONFIG;
  Line 12: export const SUPABASE_URL = SUPABASE_CONFIG.url;
  Line 13: export const SUPABASE_ANON_KEY = SUPABASE_CONFIG.anonKey;
  Line 17: export type SupabaseConfig = typeof SUPABASE_CONFIG;

lib\supabase.ts
  Line 3: import { supabase as supabaseClient } from '../src/config/supabase';
  Line 6: export const supabase = supabaseClient;
  Line 9: export const createClient = () => supabaseClient;
  Line 15: export async function getSupabaseClient() {
  Line 16: return supabase;
  Line 28: supabase.auth.startAutoRefresh();
  Line 30: supabase.auth.stopAutoRefresh();
  Line 77: const { data: { session } } = await supabase.auth.getSession();
  Line 88: const { error } = await supabase.auth.signOut();

lib\types\activities.ts
  Line 1: import { Tables } from "@dukancard-types/supabase";

lib\types\posts.ts
  Line 1: import { Tables } from "@dukancard-types/supabase";

package-lock.json
  Line 22: "@supabase/supabase-js": "^2.49.8",
  Line 4791: "node_modules/@supabase/auth-js": {
  Line 4793: "resolved": "https://registry.npmjs.org/@supabase/auth-js/-/auth-js-2.70.0.tgz",
  Line 4797: "@supabase/node-fetch": "^2.6.14"
  Line 4800: "node_modules/@supabase/functions-js": {
  Line 4802: "resolved": "https://registry.npmjs.org/@supabase/functions-js/-/functions-js-2.4.4.tgz",
  Line 4806: "@supabase/node-fetch": "^2.6.14"
  Line 4809: "node_modules/@supabase/node-fetch": {
  Line 4811: "resolved": "https://registry.npmjs.org/@supabase/node-fetch/-/node-fetch-2.6.15.tgz",
  Line 4821: "node_modules/@supabase/postgrest-js": {
  Line 4823: "resolved": "https://registry.npmjs.org/@supabase/postgrest-js/-/postgrest-js-1.19.4.tgz",
  Line 4827: "@supabase/node-fetch": "^2.6.14"
  Line 4830: "node_modules/@supabase/realtime-js": {
  Line 4832: "resolved": "https://registry.npmjs.org/@supabase/realtime-js/-/realtime-js-2.11.15.tgz",
  Line 4836: "@supabase/node-fetch": "^2.6.13",
  Line 4843: "node_modules/@supabase/realtime-js/node_modules/ws": {
  Line 4864: "node_modules/@supabase/storage-js": {
  Line 4866: "resolved": "https://registry.npmjs.org/@supabase/storage-js/-/storage-js-2.7.1.tgz",
  Line 4870: "@supabase/node-fetch": "^2.6.14"
  Line 4873: "node_modules/@supabase/supabase-js": {
  Line 4875: "resolved": "https://registry.npmjs.org/@supabase/supabase-js/-/supabase-js-2.50.2.tgz",
  Line 4879: "@supabase/auth-js": "2.70.0",
  Line 4880: "@supabase/functions-js": "2.4.4",
  Line 4881: "@supabase/node-fetch": "2.6.15",
  Line 4882: "@supabase/postgrest-js": "1.19.4",
  Line 4883: "@supabase/realtime-js": "2.11.15",
  Line 4884: "@supabase/storage-js": "2.7.1"

package.json
  Line 35: "@supabase/supabase-js": "^2.49.8",

README.md
  Line 15: Similar to the web application, the mobile app integrates with **Supabase** for backend services, including user authentication, database management, and potentially real-time features. This shared backend ensures data consistency and a unified API across both web and mobile platforms.

scripts\test-secure-keys.js
  Line 16: delete process.env.EXPO_PUBLIC_SUPABASE_URL;
  Line 17: delete process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
  Line 18: delete process.env.SUPABASE_SERVICE_ADMIN_KEY;
  Line 44: 'src/security/SecureSupabaseClient.ts',
  Line 46: 'lib/utils/supabaseAdmin.ts',
  Line 47: 'lib/supabase.ts'
  Line 72: 'lib/utils/supabaseAdmin.ts'
  Line 77: /process\.env\.EXPO_PUBLIC_SUPABASE_URL/g,
  Line 78: /process\.env\.EXPO_PUBLIC_SUPABASE_ANON_KEY/g,
  Line 79: /process\.env\.SUPABASE_SERVICE_ADMIN_KEY/g,
  Line 113: const hasEncryptedSupabase = apiKeyManagerContent.includes('decryptSupabaseKeys') &&
  Line 119: if (hasEncryptedSupabase && hasEncryptedGoogle && hasDecryptionMethod) {

src\components\business\ActivityItem.tsx
  Line 5: import { ActivityData } from '@/backend/supabase/services/activities/activityService';

src\components\business\BusinessProfileStats.tsx
  Line 6: import { realtimeService } from '@/backend/supabase/services/realtime/realtimeService';

src\components\business\BusinessStats.tsx
  Line 15: import { BusinessInteractionStatus } from "@/backend/supabase/services/business/businessInteractions";

src\components\business\NotificationsModalNew.tsx
  Line 17: import { ActivityData } from '@/backend/supabase/services/activities/activityService';

src\components\business\ProductsTab.tsx
  Line 12: import { ProductSortOption } from "@/backend/supabase/services/business/businessCardDataService";
  Line 16: import { Tables } from "@dukancard-types/supabase";

src\components\business\ReviewModal.tsx
  Line 14: import { submitBusinessReview } from '@/backend/supabase/services/business/businessInteractions';

src\components\discovery\FullScreenLocationSelector.tsx
  Line 25: } from "@/backend/supabase/services/location/locationService";
  Line 26: import { validatePincodeForCity } from "@/backend/supabase/services/common/profileService";
  Line 30: import { supabase } from "@/src/config/supabase";
  Line 182: const { data: rpcData, error: rpcError } = await supabase.rpc(
  Line 203: const { data: cityData, error } = await supabase

src\components\discovery\ResultsList.tsx
  Line 21: import { Tables } from "@/src/types/supabase";

src\components\ErrorBoundary.tsx
  Line 8: import { errorTracker } from "@/backend/supabase/services/monitoring/errorTracking";
  Line 9: import { devErrorMonitor } from "@/backend/supabase/services/monitoring/developmentErrorMonitoring";

src\components\feed\BusinessPostCreator.tsx
  Line 10: import { supabase } from '@/lib/supabase';
  Line 42: const { data: { user } } = await supabase.auth.getUser();
  Line 45: const { data: profile } = await supabase

src\components\feed\BusinessPostEditModal.tsx
  Line 20: import { uploadBusinessPostImage } from '@/backend/supabase/services/storage/businessPostImageUploadService';
  Line 21: import { supabase } from '@/lib/supabase';
  Line 90: const { data: { user } } = await supabase.auth.getUser();
  Line 93: const { data: profile } = await supabase
  Line 151: const { openCamera } = await import('@/backend/supabase/services/storage/imageUploadService');
  Line 169: const { openImageGallery } = await import('@/backend/supabase/services/storage/imageUploadService');

src\components\feed\BusinessPostModal.tsx
  Line 20: import { uploadBusinessPostImage } from '@/backend/supabase/services/storage/businessPostImageUploadService';
  Line 21: import { supabase } from '@/lib/supabase';
  Line 71: const { data: { user } } = await supabase.auth.getUser();
  Line 74: const { data: profile } = await supabase
  Line 124: const { openCamera } = await import('@/backend/supabase/services/storage/imageUploadService');
  Line 142: const { openImageGallery } = await import('@/backend/supabase/services/storage/imageUploadService');
  Line 177: const { data: { user } } = await supabase.auth.getUser();

src\components\feed\CustomerPostCreator.tsx
  Line 8: import { supabase } from "@/lib/supabase";
  Line 9: import { CustomerPostService } from "@/backend/supabase/services/customer/customerPostService";
  Line 31: const { data: { user } } = await supabase.auth.getUser();

src\components\feed\CustomerPostEditModal.tsx
  Line 20: import { uploadCustomerPostImage } from '@/backend/supabase/services/storage/customerPostImageUploadService';
  Line 21: import { supabase } from '@/lib/supabase';
  Line 82: const { data: { user } } = await supabase.auth.getUser();
  Line 85: const { data: profile } = await supabase
  Line 112: const { openCamera } = await import('@/backend/supabase/services/storage/imageUploadService');
  Line 130: const { openImageGallery } = await import('@/backend/supabase/services/storage/imageUploadService');

src\components\feed\CustomerPostModal.tsx
  Line 20: import { uploadCustomerPostImage } from '@/backend/supabase/services/storage/customerPostImageUploadService';
  Line 21: import { supabase } from '@/lib/supabase';
  Line 65: const { data: { user } } = await supabase.auth.getUser();
  Line 68: const { data: profile } = await supabase
  Line 95: const { openCamera } = await import('@/backend/supabase/services/storage/imageUploadService');
  Line 113: const { openImageGallery } = await import('@/backend/supabase/services/storage/imageUploadService');
  Line 143: const { data: { user } } = await supabase.auth.getUser();

src\components\feed\PostCard.tsx
  Line 35: } from "@/backend/supabase/services/posts/postInteractions";
  Line 39: } from "@/backend/supabase/services/products/productService";

src\components\feed\UnifiedFeedList.tsx
  Line 32: import { sharePost } from "@/backend/supabase/services/posts/postInteractions";
  Line 36: import { supabase } from "@/lib/supabase";

src\components\modals\business\components\BusinessFollowersList.tsx
  Line 15: } from "@/backend/supabase/services/business/businessSocialService";

src\components\modals\business\components\BusinessLikesList.tsx
  Line 13: } from "@/backend/supabase/services/business/businessSocialService";

src\components\modals\business\components\ProductForm.tsx
  Line 19: import { Tables, Json } from "@dukancard-types/supabase";
  Line 20: import { addProductFormSchema, updateProductFormSchema } from "@/backend/supabase/services/business/schemas";
  Line 21: import { PlanLimitInfo, getPlanDisplayName, getPlanLimitDisplayText } from "@/backend/supabase/services/business/planLimitService";

src\components\modals\business\components\ProductsList.tsx
  Line 13: import { Tables } from "@dukancard-types/supabase";
  Line 16: import { ProductsServices, getBusinessProducts, deleteProductService } from "@/backend/supabase/services/business/businessProductsService";

src\components\modals\business\components\VariantForm.tsx
  Line 31: import { Tables } from "@dukancard-types/supabase";

src\components\modals\business\components\VariantList.tsx
  Line 14: import { Tables } from "@dukancard-types/supabase";
  Line 16: import { getProductVariants, deleteProductVariant } from "@/backend/supabase/services/business/variantService";
  Line 18: import { Json } from "@/src/types/supabase";

src\components\modals\business\GalleryModal.tsx
  Line 3: import { getBusinessGalleryImages } from '../../../../backend/supabase/services/gallery/galleryService';

src\components\modals\business\ManageCardModal.tsx
  Line 31: } from "@/backend/supabase/services/business/schemas";

src\components\modals\business\ManageProductsModal.tsx
  Line 29: } from "@/backend/supabase/services/business/businessProductsService";
  Line 30: import { getProductVariants } from "@/backend/supabase/services/business/variantService";
  Line 34: } from "@/backend/supabase/services/business/planLimitService";
  Line 35: import { Tables } from "@dukancard-types/supabase";

src\components\modals\business\sections\AdvancedFeaturesSection.tsx
  Line 13: import { getAdvancedFeaturesData } from "@/backend/supabase/services/business/businessCardService";

src\components\modals\business\sections\AppearanceSection.tsx
  Line 16: } from "@/backend/supabase/services/business/businessCardService";
  Line 17: import { AppearanceData, appearanceSchema } from "@/backend/supabase/services/business/schemas";

src\components\modals\business\sections\BasicInfoSection.tsx
  Line 22: } from "@/backend/supabase/services/storage/avatarUploadService";
  Line 27: } from "@/backend/supabase/services/business/businessCardService";
  Line 28: import { BasicInfoData, basicInfoSchema } from "@/backend/supabase/services/business/schemas";

src\components\modals\business\sections\BusinessDetailsSection.tsx
  Line 18: } from "@/backend/supabase/services/business/businessCardService";
  Line 19: import { BusinessDetailsData, businessDetailsSchema } from "@/backend/supabase/services/business/schemas";

src\components\modals\business\sections\ContactLocationSection.tsx
  Line 17: } from "@/backend/supabase/services/business/businessCardService";
  Line 18: import { ContactLocationData, contactLocationSchema } from "@/backend/supabase/services/business/schemas";

src\components\modals\business\sections\SocialLinksSection.tsx
  Line 17: } from "@/backend/supabase/services/business/businessCardService";
  Line 18: import { SocialLinksData, socialLinksSchema } from "@/backend/supabase/services/business/schemas";

src\components\modals\business\sections\StatusSettingsSection.tsx
  Line 18: } from "@/backend/supabase/services/business/businessCardService";
  Line 19: import { StatusSettingsData, statusSettingsSchema } from "@/backend/supabase/services/business/schemas";

src\components\modals\business\VariantModal.tsx
  Line 10: import { Tables } from "@dukancard-types/supabase";

src\components\modals\customer\components\FollowingList.tsx
  Line 14: } from "@/backend/supabase/services/posts/socialService";

src\components\modals\customer\components\LikesList.tsx
  Line 12: } from "@/backend/supabase/services/posts/socialService";

src\components\modals\customer\components\ReviewsList.tsx
  Line 12: } from "@/backend/supabase/services/posts/socialService";

src\components\modals\customer\EditProfileModal.tsx
  Line 42: } from "@/backend/supabase/services/storage/avatarUploadService";
  Line 44: import { cleanAddressData } from "@/backend/supabase/utils/addressValidation";
  Line 49: } from "@/backend/supabase/services/customer/customerProfileService";
  Line 50: import { supabase } from "@/lib/supabase";
  Line 414: const { error: authUpdateError } = await supabase.auth.updateUser({

src\components\modals\customer\ReviewsModal.tsx
  Line 21: import { reviewsService } from "@/backend/supabase/services/posts/socialService";

src\components\notifications\NotificationPreferences.tsx
  Line 15: import { supabase } from '@/lib/supabase';

src\components\post\SinglePostScreen.tsx
  Line 17: import { sharePost } from '@/backend/supabase/services/posts/postInteractions';

src\components\product\ProductRecommendations.tsx
  Line 6: import { ProductsServices } from "@/backend/supabase/services/business/businessProductsService";

src\components\profile\ActivityCard.tsx
  Line 19: import { Tables } from "@dukancard-types/supabase";

src\components\profile\AddressForm.tsx
  Line 16: import { updateCustomerAddress, validatePincode } from '@/backend/supabase/services/common/profileService';

src\components\profile\AvatarUpload.tsx
  Line 15: import { updateCustomerAvatar } from '@/backend/supabase/services/common/profileService';
  Line 16: import { uploadAvatarImage } from '@/backend/supabase/services/storage/avatarUploadService';

src\components\profile\AvatarUploadSection.tsx
  Line 7: import { deleteCustomerAvatar } from "@/backend/supabase/services/storage/avatarUploadService";

src\components\profile\AvatarUploadWithCrop.tsx
  Line 14: import { openCameraForAvatar, openGalleryForAvatar } from '@/backend/supabase/services/storage/avatarUploadService';

src\components\profile\ProfileForm.tsx
  Line 14: import { updateCustomerProfile } from '@/backend/supabase/services/common/profileService';

src\components\qr\QRScannerModal.tsx
  Line 17: import { QRScanService } from '@/backend/supabase/services/common/qrScanService';

src\components\settings\EmailLinkingSection.tsx
  Line 14: import { linkCustomerEmail, verifyEmailOTP } from '@/backend/supabase/services/common/settingsService';

src\components\settings\PasswordManagementSection.tsx
  Line 14: import { updatePassword } from '@/backend/supabase/services/common/settingsService';

src\components\settings\PhoneLinkingSection.tsx
  Line 14: import { linkCustomerPhone, verifyPhoneOTP } from '@/backend/supabase/services/common/settingsService';

src\components\shared\ui\index.ts
  Line 2: export type { ProductData } from '@/backend/supabase/services/products/productService';

src\components\shared\ui\ProductCard.tsx
  Line 10: import { ProductsServices } from "@/backend/supabase/services/business/businessProductsService";

src\components\social\ReviewCard.tsx
  Line 17: import { ReviewBusinessProfile } from '@/backend/supabase/services/posts/socialService';

src\components\ui\AvatarUpload.tsx
  Line 13: import { openCameraForAvatar, openGalleryForAvatar } from '@/backend/supabase/services/storage/avatarUploadService';

src\components\ui\FormField.tsx
  Line 2: import { ValidationFunction, ValidationResult } from '@/backend/supabase/utils/validation';

src\components\ui\LocationDisplay.tsx
  Line 5: import { supabase } from '@/lib/supabase';
  Line 37: const { data: businessProfile } = await supabase
  Line 50: const { data: customerProfile } = await supabase

src\components\ui\LocationPicker.tsx
  Line 6: } from '@/backend/supabase/services/location/locationService';

src\config\publicKeys.ts
  Line 17: export const SUPABASE_CONFIG = {
  Line 18: url: 'https://rnjolcoecogzgglnblqn.supabase.co',
  Line 51: export type SupabaseConfig = typeof SUPABASE_CONFIG;

src\config\supabase.ts
  Line 6: import { createClient } from '@supabase/supabase-js';
  Line 8: import { SUPABASE_CONFIG } from './publicKeys';
  Line 11: export const supabase = createClient(
  Line 12: SUPABASE_CONFIG.url,
  Line 13: SUPABASE_CONFIG.anonKey,
  Line 33: export default supabase;

src\contexts\AuthContext.tsx
  Line 1: import { supabase } from "@/lib/supabase";
  Line 2: import { getUserRoleStatus } from "@/backend/supabase/services/common/userRoleStatusService";
  Line 3: import { Session, User } from "@supabase/supabase-js";
  Line 62: supabase.auth.getSession().then(({ data: { session } }) => {
  Line 74: } = supabase.auth.onAuthStateChange(async (event, session) => {
  Line 141: const { error } = await supabase.auth.signInWithPassword({
  Line 179: const { error } = await supabase.auth.signUp({
  Line 218: "@/backend/supabase/services/auth/nativeGoogleAuth2025"
  Line 226: const { error } = await supabase.auth.signOut();
  Line 235: const { error } = await supabase.auth.signInWithOAuth({
  Line 274: const { data, error } = await supabase
  Line 281: const { data, error } = await supabase
  Line 344: } = await supabase.auth.getSession();
  Line 351: } = await supabase.auth.getUser();
  Line 355: await supabase.auth.signOut();
  Line 381: await supabase.auth.signOut();

src\contexts\DiscoveryContext.tsx
  Line 14: import { supabase } from "../config/supabase";
  Line 29: import { Tables } from "../types/supabase";
  Line 101: const { data: customerProfile } = await supabase
  Line 121: const { data: businessProfile } = await supabase

src\contexts\LocationContext.tsx
  Line 18: } from "@/backend/supabase/services/location/locationService";

src\contexts\NotificationContext.tsx
  Line 3: import { activityService, ActivityData } from '@/backend/supabase/services/activities/activityService';
  Line 4: import { realtimeService } from '@/backend/supabase/services/realtime/realtimeService';

src\contexts\OnboardingContext.tsx
  Line 8: } from '@/backend/supabase/services/common/onboardingService';

src\hooks\useAuthErrorHandler.ts
  Line 3: import { handleNetworkError, handleSupabaseError, logError, AppError } from '@/src/utils/errorHandling';
  Line 100: else if (error.code || error.message?.includes('supabase')) {
  Line 101: processedError = handleSupabaseError(error);

src\hooks\useAvatarUpload.ts
  Line 10: } from '@/backend/supabase/services/storage/avatarUploadService';

src\hooks\useBusinessCardData.ts
  Line 10: } from '@/backend/supabase/services/business/businessCardDataService';
  Line 11: import { fetchCustomAd } from '@/backend/supabase/services/ads/adService';
  Line 14: import { Tables } from '@/src/types/supabase';

src\hooks\useBusinessInteractions.ts
  Line 7: } from '@/backend/supabase/services/business/businessInteractions';
  Line 8: import { supabase } from '@/lib/supabase';
  Line 25: const { data: { user }, error: authError } = await supabase.auth.getUser();

src\hooks\useLocationPermission.ts
  Line 5: } from '@/backend/supabase/services/location/locationService';

src\hooks\usePincodeDetails.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 40: const { data: pincodeData, error: pincodeError } = await supabase

src\hooks\usePostOwnership.ts
  Line 2: import { supabase } from '@/lib/supabase';
  Line 31: const { data: { user }, error } = await supabase.auth.getUser();

src\hooks\useSlugValidation.ts
  Line 1: import { checkSlugAvailability } from '@/backend/supabase/services/common/onboardingService';

src\services\discovery\businessActions.ts
  Line 1: import { Tables } from "../../types/supabase";

src\services\discovery\DiscoveryService.ts
  Line 7: import { supabase } from "../../config/supabase";
  Line 60: private supabase = supabase;
  Line 152: let countQuery = this.supabase
  Line 157: let businessQuery = this.supabase
  Line 232: let businessQuery = this.supabase
  Line 280: let countQuery = this.supabase
  Line 312: let productsQuery = this.supabase

src\services\discovery\locationActions.ts
  Line 1: import { supabase } from "../../config/supabase";
  Line 84: const { data } = await supabase
  Line 135: let countQuery = supabase
  Line 154: let businessQuery = supabase
  Line 363: let businessIdsQuery = supabase
  Line 421: let countQuery = supabase
  Line 457: let productsQuery = supabase

src\services\discovery\productActions.ts
  Line 1: import { supabase } from "../../config/supabase";
  Line 176: let businessQuery = supabase
  Line 224: let countQuery = supabase
  Line 250: let productsQuery = supabase
  Line 369: const { data: validBusinesses, error: businessError } = await supabase
  Line 401: let countQuery = supabase
  Line 422: let productsQuery = supabase

src\services\discovery\types.ts
  Line 1: import { Tables } from "../../types/supabase";

src\services\discovery\utils\locationUtils.ts
  Line 1: import { supabase } from "../../../config/supabase";
  Line 21: const { data: pincodeData, error: pincodeError } = await supabase

src\services\discovery\utils\secureBusinessProfiles.ts
  Line 1: import { supabase } from "../../../config/supabase";
  Line 2: import { Tables } from "../../../types/supabase";
  Line 128: let countQuery = supabase
  Line 133: let businessQuery = supabase
  Line 281: let countQuery = supabase
  Line 315: let businessQuery = supabase
  Line 402: let validBusinessQuery = supabase

src\services\locationStorageService.ts
  Line 13: } from "@/backend/supabase/services/location/locationService";

src\types\auth.ts
  Line 6: import { Tables } from "./supabase";

src\types\business.ts
  Line 1: import { Database, Tables } from "./supabase";

src\types\discovery.ts
  Line 1: import { Tables, Json } from "./supabase";

src\types\supabase.ts
  Line 12: __InternalSupabase: {
  Line 1745: type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

src\utils\apiClient.ts
  Line 9: import { supabase } from '@/lib/supabase';
  Line 40: const { data: { session }, error: sessionError } = await supabase.auth.getSession();

src\utils\deletePostMedia.ts
  Line 1: import { supabase } from '@/lib/supabase';
  Line 2: import { getPostFolderPath } from '@/backend/supabase/utils/storage-paths';
  Line 18: const { data: files, error: listError } = await supabase.storage
  Line 43: const { error: deleteError } = await supabase.storage
  Line 80: const { data: files, error: listError } = await supabase.storage
  Line 105: const { error: deleteError } = await supabase.storage

src\utils\errorHandling.ts
  Line 2: import { AuthError } from "@supabase/supabase-js";
  Line 225: export function handleSupabaseError(error: AuthError | Error | null): AppError {
  Line 226: console.error('Supabase error:', error);

src\utils\qrCodeUtils.ts
  Line 1: import { validateBusinessSlug } from '@/backend/supabase/utils/validation';

src\utils\userProfileUtils.ts
  Line 1: import { SupabaseClient } from "@supabase/supabase-js";
  Line 2: import { Tables } from "../types/supabase";
  Line 27: supabaseClient: SupabaseClient
  Line 31: const { data: businessProfile, error: businessError } = await supabaseClient
  Line 47: const { data: customerProfile, error: customerError } = await supabaseClient
