import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import GalleryPageClient from "./GalleryPageClient";
import { getGalleryImages } from "./actions";
import { Metadata } from "next";
import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
import { getBusinessProfileForGallery, getLatestPaymentSubscription } from "@/lib/supabase/services/businessService";

export const metadata: Metadata = {
  title: "Gallery - Dukancard Business",
  description: "Manage your business photo gallery",
  robots: "noindex, nofollow",
};

export default async function GalleryPage() {
  const supabase = await createClient();

  const { user, error: _userError } = await getAuthenticatedUser(supabase);

  if (!user) {
    return redirect("/login?message=Authentication required");
  }

  // Fetch the business profile
  const { data: profileData, error: profileError } = await getBusinessProfileForGallery(supabase, user.id);

  if (profileError) {
    console.error("Error fetching business profile:", profileError);
    return redirect("/dashboard/business?message=Failed to load profile");
  }

  // Get the current plan from payment_subscriptions
  const { data: subscriptionData, error: subscriptionError } = await getLatestPaymentSubscription(supabase, user.id);

  if (subscriptionError) {
    console.error("Error fetching subscription data:", subscriptionError);
  }

  // Default to free plan if no subscription found
  const planId = subscriptionData?.plan_id || "free";

  // Fetch gallery images
  const { images, error } = await getGalleryImages();

  if (error) {
    console.error("Error fetching gallery images:", error);
  }

  return (
    <div className="w-full max-w-screen-xl mx-auto">
      <GalleryPageClient
        initialImages={images || []}
        userPlan={planId}
        businessName={profileData?.business_name || "Your Business"}
      />
    </div>
  );
}
