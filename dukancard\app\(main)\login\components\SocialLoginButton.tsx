"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { signInWithOAuth } from "@/lib/supabase/services/sharedService";

interface SocialLoginButtonProps {
  redirectSlug?: string | null;
  message?: string | null;
  disabled?: boolean;
}

export function SocialLoginButton({ redirectSlug, message, disabled }: SocialLoginButtonProps) {
  const [isSocialLoading, setIsSocialLoading] = useState<"google" | null>(null);

  // Initiates the Supabase OAuth flow for social login.
  // This function gets the authorization URL and opens it in a new tab.
  // After successful authentication, Supabase will redirect to /auth/callback,
  // where the unified post-login redirection logic is executed.
  async function handleSocialLogin(provider: "google") {
    try {
      setIsSocialLoading(provider);
      const supabase = createClient();

      // Construct the callback URL with the redirect and message parameters if available
      // Add closeWindow=true to indicate this window should close after auth
      let callbackUrl = `${window.location.origin}/auth/callback?closeWindow=true`;

      // Add redirect parameter if available
      if (redirectSlug) {
        callbackUrl += `&redirect=${encodeURIComponent(redirectSlug)}`;
      }

      // Add message parameter if available
      if (message) {
        callbackUrl += `&message=${encodeURIComponent(message)}`;
      }

      // Get the authorization URL but don't redirect automatically
      const { data, error } = await signInWithOAuth(supabase, provider, callbackUrl, {
        access_type: "offline",
        prompt: "select_account",
      });

      if (error) {
        toast.error("Login failed", {
          description: error.message,
        });
        setIsSocialLoading(null);
        return;
      }

      // If we have the URL, open it in a new tab
      if (data?.url) {
        // Open the authorization URL in a new tab
        window.open(data.url, "_blank");

        // Show a toast to guide the user
        toast.info("Google sign-in opened in a new tab", {
          description: "Please complete the sign-in process in the new tab.",
          duration: 5000,
        });

        // Reset loading state after a short delay
        setTimeout(() => {
          setIsSocialLoading(null);
        }, 1000);
      } else {
        toast.error("Failed to start Google sign-in", {
          description: "Please try again or use email login.",
        });
        setIsSocialLoading(null);
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unexpected error occurred. Please try again.";
      toast.error("Login failed", {
        description: errorMessage,
      });
      setIsSocialLoading(null);
    }
  }

  return (
    <div className="flex justify-center mb-5 sm:mb-6">
      <Button
        variant="outline"
        className="cursor-pointer bg-background hover:bg-muted border-border dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:border-neutral-800 w-full py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-foreground"
        onClick={() => handleSocialLogin("google")}
        disabled={!!isSocialLoading || disabled}
      >
        {isSocialLoading === "google" ? (
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
        ) : (
          <>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              className="w-4 h-4 mr-2"
            >
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            Login with Google
          </>
        )}
      </Button>
    </div>
  );
}
