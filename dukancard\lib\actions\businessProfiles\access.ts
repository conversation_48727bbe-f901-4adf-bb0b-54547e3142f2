"use server";

import { createClient } from "@/utils/supabase/server";

/**
 * Check if the current user has access to a business profile
 * This is used to verify ownership before allowing certain operations
 */
export async function checkBusinessProfileAccess(
  businessProfileId: string
): Promise<{
  hasAccess: boolean;
  error?: string;
}> {
  try {
    // Get the current user
    const supabase = await createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return { hasAccess: false, error: "Authentication required" };
    }

    // Check if the user owns the business profile
    const { data, error } = await supabase
      .from("business_profiles")
      .select("id")
      .eq("id", businessProfileId)
      .eq("id", user.id) // Business profile ID should match user ID
      .maybeSingle();

    if (error) {
      console.error("Access check error:", error);
      return { hasAccess: false, error: "Error checking access" };
    }

    return { hasAccess: !!data };
  } catch (e) {
    console.error("Exception in checkBusinessProfileAccess:", e);
    return { hasAccess: false, error: "An unexpected error occurred" };
  }
}

/**
 * Get the current user's business profile ID
 * This is useful for operations that need to know the user's business profile
 */
export async function getCurrentUserBusinessProfileId(): Promise<{
  profileId?: string;
  error?: string;
}> {
  try {
    // Get the current user
    const supabase = await createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return { error: "Authentication required" };
    }

    // Check if the user has a business profile
    const { data, error } = await supabase
      .from("business_profiles")
      .select("id")
      .eq("id", user.id)
      .maybeSingle();

    if (error) {
      console.error("Profile ID fetch error:", error);
      return { error: "Error fetching profile ID" };
    }

    if (!data) {
      return { error: "Business profile not found" };
    }

    return { profileId: data.id };
  } catch (e) {
    console.error("Exception in getCurrentUserBusinessProfileId:", e);
    return { error: "An unexpected error occurred" };
  }
}
