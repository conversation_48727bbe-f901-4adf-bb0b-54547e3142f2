Files in dukancard project using Supabase (excluding service files):
Total files found: 246

.vscode\settings.json
  Line 3: "supabase/functions"

app\(auth)\choose-role\actions.ts
  Line 4: import { checkIfCustomerProfileExists, createUserProfile } from "@/lib/supabase/services/customerService";
  Line 5: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 7: import { COLUMNS } from "@/lib/supabase/constants";
  Line 20: const { user, error: userError } = await getAuthenticatedUser(supabase);
  Line 28: const { exists, error: checkError } = await checkIfCustomerProfileExists(supabase, userId);
  Line 44: const { error: insertError } = await createUserProfile(supabase, {

app\(auth)\choose-role\page.tsx
  Line 4: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 5: import { checkIfCustomerProfileExists } from "@/lib/supabase/services/customerService";
  Line 6: import { checkIfBusinessProfileExists } from "@/lib/supabase/services/businessService";
  Line 29: const { user, error: _userError } = await getAuthenticatedUser(supabase);
  Line 38: checkIfCustomerProfileExists(supabase, user.id),
  Line 39: checkIfBusinessProfileExists(supabase, user.id),

app\(auth)\layout.tsx
  Line 7: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 18: const { user } = await getAuthenticatedUser(supabase);

app\(dashboard)\dashboard\business\activities\page.tsx
  Line 1: import { getAuthenticatedUser } from '@/lib/supabase/services/sharedService';
  Line 15: const { user, error: authError } = await getAuthenticatedUser(supabase);

app\(dashboard)\dashboard\business\analytics\actions.ts
  Line 3: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 4: import { getBusinessProfileAnalyticsData, getDailyUniqueVisitTrend, getHourlyUniqueVisitTrend, getMonthlyUniqueVisits, getMonthlyUniqueVisitTrend, getAvailableYearsForMonthlyMetrics, getTotalUniqueVisits } from "@/lib/supabase/services/businessService";
  Line 143: const { user, error: userError } = await getAuthenticatedUser(supabase);
  Line 154: const { data: profile, error: profileError } = await getBusinessProfileAnalyticsData(supabase, businessProfileId);
  Line 190: supabase,
  Line 200: supabase,
  Line 210: supabase,
  Line 220: supabase,
  Line 229: supabase,
  Line 244: supabase,
  Line 259: supabase,
  Line 291: supabase,

app\(dashboard)\dashboard\business\analytics\page.tsx
  Line 4: } from "@/lib/supabase/services/sharedService";
  Line 8: } from "@/lib/supabase/services/businessService";
  Line 23: const { user, error: userError } = await getAuthenticatedUser(supabase);
  Line 31: await getBusinessProfileWithInteractionMetrics(supabase, user.id);
  Line 39: await getLatestSubscription(supabase, user.id);

app\(dashboard)\dashboard\business\card\actions\customAdUpload.ts
  Line 3: import { uploadFileToStorage, getPublicUrlFromStorage, removeFileFromStorage } from "@/lib/supabase/services/sharedService";
  Line 4: import { getBusinessProfileCustomAds, updateBusinessProfile } from "@/lib/supabase/services/businessService";
  Line 5: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 9: import { BUCKETS } from "@/lib/supabase/constants";
  Line 10: import { Tables } from "@/types/supabase";
  Line 31: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 68: supabase,
  Line 84: supabase,
  Line 96: const { error: updateError } = await updateBusinessProfile(supabase, user.id, {
  Line 129: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 149: const { data: customAds, error: fetchError } = await getBusinessProfileCustomAds(supabase, user.id);
  Line 163: const { error: updateError } = await updateBusinessProfile(supabase, user.id, { custom_ads: updatedCustomAds as Tables<'business_profiles'>['custom_ads'] });
  Line 189: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 198: const { data: customAds, error: fetchError } = await getBusinessProfileCustomAds(supabase, user.id);
  Line 212: const { error: updateError } = await updateBusinessProfile(supabase, user.id, { custom_ads: updatedCustomAds as Tables<'business_profiles'>['custom_ads'] });
  Line 238: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 247: const { data: customAds, error: fetchError } = await getBusinessProfileCustomAds(supabase, user.id);
  Line 265: supabase,
  Line 279: const { error: updateError } = await updateBusinessProfile(supabase, user.id, {

app\(dashboard)\dashboard\business\card\actions\customHeaderUpload.ts
  Line 3: import { uploadFileToStorage, getPublicUrlFromStorage, removeFileFromStorage, getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 4: import { getBusinessProfileCustomBranding, updateBusinessProfile } from "@/lib/supabase/services/businessService";
  Line 6: import { Tables } from "@/types/supabase";
  Line 9: import { BUCKETS } from "@/lib/supabase/constants";
  Line 30: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 69: supabase,
  Line 85: supabase,
  Line 97: const { data: currentBranding, error: fetchError } = await getBusinessProfileCustomBranding(supabase, user.id);
  Line 112: const { error: updateError } = await updateBusinessProfile(supabase, user.id, {
  Line 140: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 149: const { data: currentBranding, error: fetchError } = await getBusinessProfileCustomBranding(supabase, user.id);
  Line 167: supabase,
  Line 186: const { error: updateError } = await updateBusinessProfile(supabase, user.id, {

app\(dashboard)\dashboard\business\card\actions\themeHeaderActions.ts
  Line 3: import { uploadFileToStorage, getPublicUrlFromStorage, removeFileFromStorage, getAuthenticatedUser, listStorageFiles } from "@/lib/supabase/services/sharedService";
  Line 7: import { BUCKETS } from "@/lib/supabase/constants";
  Line 31: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 62: supabase,
  Line 78: supabase,
  Line 114: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 139: supabase,
  Line 188: supabase,
  Line 220: supabase,

app\(dashboard)\dashboard\business\card\actions\themeSpecificHeaderUpload.ts
  Line 3: import { uploadFileToStorage, getPublicUrlFromStorage, removeFileFromStorage, getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 4: import { getBusinessProfileCustomBranding, updateBusinessProfile } from "@/lib/supabase/services/businessService";
  Line 6: import { Tables } from "@/types/supabase";
  Line 9: import { BUCKETS } from "@/lib/supabase/constants";
  Line 31: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 71: supabase,
  Line 87: supabase,
  Line 99: const { data: currentBranding, error: fetchError } = await getBusinessProfileCustomBranding(supabase, user.id);
  Line 116: const { error: updateError } = await updateBusinessProfile(supabase, user.id, {
  Line 150: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 159: const { data: currentBranding, error: fetchError } = await getBusinessProfileCustomBranding(supabase, user.id);
  Line 182: supabase,
  Line 212: const { error: updateError } = await updateBusinessProfile(supabase, user.id, {

app\(dashboard)\dashboard\business\card\business-card\getBusinessCardData.ts
  Line 3: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 4: import { getBusinessProfileWithAllDetails, updateBusinessProfile } from "@/lib/supabase/services/businessService";
  Line 19: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 25: const { data, error } = await getBusinessProfileWithAllDetails(supabase, user.id);
  Line 31: console.error("Supabase Fetch Error:", error);
  Line 65: const { error: updateError } = await updateBusinessProfile(supabase, user.id, { status: "offline" });

app\(dashboard)\dashboard\business\card\business-card\updateBusinessCard.ts
  Line 3: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 4: import { getBusinessProfilePhone, updateBusinessProfile, getBusinessProfileCustomBranding } from "@/lib/supabase/services/businessService";
  Line 36: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 43: const { phone: _existingPhone, error: profileError } = await getBusinessProfilePhone(supabase, user.id);
  Line 125: const { data: currentProfileBranding } = await getBusinessProfileCustomBranding(supabase, user.id);
  Line 135: const { data: currentProfileBranding } = await getBusinessProfileCustomBranding(supabase, user.id);
  Line 176: const { data: updatedProfile, error: updateError } = await updateBusinessProfile(supabase, user.id, dataToUpdate);
  Line 179: console.error("Supabase Update Error:", updateError);

app\(dashboard)\dashboard\business\card\data\businessCardMapper.ts
  Line 3: import { Json } from "@/types/supabase";

app\(dashboard)\dashboard\business\card\data\subscriptionChecker.ts
  Line 3: import { getLatestSubscriptionStatus } from "@/lib/supabase/services/businessService";
  Line 17: const { subscriptionStatus, error: subscriptionError } = await getLatestSubscriptionStatus(supabase, userId);
  Line 46: const { subscriptionStatus, error: subscriptionError } = await getLatestSubscriptionStatus(supabase, userId);

app\(dashboard)\dashboard\business\card\logo\logoActions.ts
  Line 9: import { BUCKETS } from "@/lib/supabase/constants";
  Line 10: import { getAuthenticatedUser, listStorageFiles, removeFileFromStorage, uploadFileToStorage, getPublicUrlFromStorage } from "@/lib/supabase/services/sharedService";
  Line 11: import { updateBusinessLogoUrl, getBusinessLogoUrl } from "@/lib/supabase/services/businessService";
  Line 23: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 29: const { error: updateError } = await updateBusinessLogoUrl(supabase, user.id, logoUrl);
  Line 49: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 56: const { logoUrl: currentLogoUrl, error: fetchError } = await getBusinessLogoUrl(supabase, user.id);
  Line 71: const { error: deleteError } = await removeFileFromStorage(supabase, BUCKETS.BUSINESS, [filePath]);
  Line 87: const { error: updateError } = await updateBusinessLogoUrl(supabase, user.id, null);
  Line 110: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 139: const { data: files, error: listError } = await listStorageFiles(supabase, BUCKETS.BUSINESS, profileFolderPath, { limit: 10 });
  Line 147: const { error: deleteError } = await removeFileFromStorage(supabase, BUCKETS.BUSINESS, filesToDelete);
  Line 162: supabase,
  Line 178: const { publicUrl, error: urlError } = await getPublicUrlFromStorage(supabase, BUCKETS.BUSINESS, fullPath);

app\(dashboard)\dashboard\business\card\page.tsx
  Line 6: import { getLatestSubscriptionStatus } from "@/lib/supabase/services/businessService";
  Line 32: const { subscriptionStatus, error: subscriptionError } = await getLatestSubscriptionStatus(supabase, cardData.id || "");

app\(dashboard)\dashboard\business\card\public\publicCardActions.ts
  Line 3: import { getBusinessProfileIdAndStatusBySlug, getSecureBusinessProfileWithProductsBySlug } from "@/lib/supabase/services/businessService";
  Line 26: const { data: businessStatusData, error: statusError } = await getBusinessProfileIdAndStatusBySlug(supabase, slug);
  Line 38: const { data, error } = await getSecureBusinessProfileWithProductsBySlug(supabase, slug);

app\(dashboard)\dashboard\business\card\utils\constants.ts
  Line 1: import { BUCKETS } from "@/lib/supabase/constants";

app\(dashboard)\dashboard\business\components\DashboardOverviewClient.tsx
  Line 8: import * as userService from "@/lib/supabase/services/sharedService";
  Line 9: import { TABLES } from "@/lib/supabase/constants";
  Line 11: import { RealtimePostgresChangesPayload } from "@supabase/supabase-js";
  Line 86: const supabase = createClient();
  Line 88: supabase,

app\(dashboard)\dashboard\business\gallery\actions.ts
  Line 3: import { getAuthenticatedUser, uploadFileToStorage, getPublicUrlFromStorage, removeFileFromStorage } from "@/lib/supabase/services/sharedService";
  Line 4: import { getLatestSubscriptionStatus, getBusinessProfileGallery, updateBusinessProfile } from "@/lib/supabase/services/businessService";
  Line 15: import { BUCKETS } from "@/lib/supabase/constants";
  Line 16: import { Json } from "@/types/supabase";
  Line 26: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 52: const { subscriptionStatus, error: subscriptionError } = await getLatestSubscriptionStatus(supabase, userId);
  Line 62: const { data: profileGalleryData, error: profileError } = await getBusinessProfileGallery(supabase, userId);
  Line 90: supabase,
  Line 107: const { publicUrl, error: urlError } = await getPublicUrlFromStorage(supabase, BUCKETS.BUSINESS, imagePath);
  Line 127: const { error: updateError } = await updateBusinessProfile(supabase, userId, { gallery: updatedGallery as Json });
  Line 134: supabase,
  Line 166: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 176: const { error: updateError } = await updateBusinessProfile(supabase, userId, { gallery: orderedImages as Json });
  Line 202: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 210: const { data: profileData, error: profileError } = await getBusinessProfileGallery(supabase, user.id);
  Line 231: supabase,
  Line 254: const { error: updateError } = await updateBusinessProfile(supabase, user.id, { gallery: updatedGallery as Json });
  Line 286: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 293: const { data: profileData, error: profileError } = await getBusinessProfileGallery(supabase, user.id);

app\(dashboard)\dashboard\business\gallery\page.tsx
  Line 6: import { COLUMNS, TABLES } from "@/lib/supabase/constants";
  Line 19: } = await supabase.auth.getUser();
  Line 26: const { data: profileData, error: profileError } = await supabase
  Line 38: const { data: subscriptionData, error: subscriptionError } = await supabase

app\(dashboard)\dashboard\business\layout.tsx
  Line 26: } = await supabase.auth.getUser();
  Line 30: const { data: profile, error } = await supabase
  Line 50: const { data: subscription } = await supabase

app\(dashboard)\dashboard\business\likes\actions.ts
  Line 1: import { getBusinessLikes, getBusinessLikesCount, getMyLikes, getMyLikesCount, getBusinessProfilesByIds } from '@/lib/supabase/services/businessService';
  Line 2: import { getCustomerProfilesByIds } from '@/lib/supabase/services/customerService';
  Line 3: import { Tables } from '@/types/supabase';
  Line 56: const totalCount = await getBusinessLikesCount(supabase, businessId);
  Line 67: const likes = await getBusinessLikes(supabase, businessId, page, limit);
  Line 81: getCustomerProfilesByIds(supabase, userIds),
  Line 82: getBusinessProfilesByIds(supabase, userIds)
  Line 141: const totalCount = await getMyLikesCount(supabase, businessId, searchTerm);
  Line 152: const likesWithProfiles = await getMyLikes(supabase, businessId, page, limit, searchTerm);

app\(dashboard)\dashboard\business\likes\page.tsx
  Line 35: } = await supabase.auth.getUser();

app\(dashboard)\dashboard\business\overview\page.tsx
  Line 80: } = await supabase.auth.getUser();
  Line 87: const { data: profileData, error: profileError } = await supabase
  Line 96: const { data: subscription, error: subscriptionError } = await supabase

app\(dashboard)\dashboard\business\page.tsx
  Line 16: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 23: const { data: businessProfile, error: profileError } = await supabase

app\(dashboard)\dashboard\business\plan\components\EnhancedInvoiceHistoryCard.tsx
  Line 192: const supabase = createClient();
  Line 193: const { data: subscriptions, error: subscriptionError } = await supabase

app\(dashboard)\dashboard\business\plan\page.tsx
  Line 140: } = await supabase.auth.getUser();
  Line 147: const { data: profile, error: profileError } = await supabase
  Line 201: const { data: subscription, error: subscriptionError } = await supabase

app\(dashboard)\dashboard\business\products\actions\addProduct.ts
  Line 3: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 4: import { insertProduct, updateProduct, getProductById } from "@/lib/supabase/services/businessService";
  Line 8: import { COLUMNS } from "@/lib/supabase/constants";
  Line 16: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 98: const { data: insertedProduct, error: insertError } = await insertProduct(supabase, dataToInsert);
  Line 158: const { data: _updateResult, error: updateImageError } = await updateProduct(supabase, productId, updateData);
  Line 177: const { data: latestProduct, error: fetchError } = await getProductById(supabase, productId);

app\(dashboard)\dashboard\business\products\actions\addVariant.ts
  Line 4: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 12: } from "@/lib/supabase/services/businessService";
  Line 16: import { COLUMNS } from "@/lib/supabase/constants";
  Line 17: import { Tables, TablesInsert } from "@/types/supabase";
  Line 19: import { PostgrestError } from "@supabase/supabase-js";
  Line 28: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 82: const { data: product, error: productError } = await getProductByIdAndBusinessId(supabase, product_id, user.id);
  Line 90: const { data: existingVariants, error: checkError } = await getExistingProductVariants(supabase, product_id);
  Line 149: const { data: newVariant, error: insertError }: { data: Tables<'product_variants'> | null; error: string | PostgrestError | null } = await insertProductVariant(supabase, variantData);
  Line 170: await deleteProductVariant(supabase, newVariant.id as string);
  Line 186: supabase,
  Line 241: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 248: const { data: product, error: productError } = await getProductByIdAndBusinessId(supabase, product_id, user.id);
  Line 267: const { data: newVariants, error: insertError } = await insertMultipleProductVariants(supabase, variantDataArray);

app\(dashboard)\dashboard\business\products\actions\bulkVariantOperations.ts
  Line 3: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 4: import { getVariantsWithProductBusinessId, getBusinessProfileById, bulkUpdateProductVariants, updateProductVariant } from "@/lib/supabase/services/businessService";
  Line 7: import { COLUMNS } from "@/lib/supabase/constants";
  Line 42: const { user, error: userError } = await getAuthenticatedUser(supabase);
  Line 52: const { data: variants, error: variantsError } = await getVariantsWithProductBusinessId(supabase, validatedData.variantIds);
  Line 81: const { data: userBusiness, error: businessError } = await getBusinessProfileById(supabase, user.id);
  Line 151: const { success, error } = await updateProductVariant(supabase, variant.id, { [COLUMNS.DISCOUNTED_PRICE]: discountedPrice });
  Line 179: const { data: updatedVariants, error: updateError } = await bulkUpdateProductVariants(supabase, validatedData.variantIds, updateData);
  Line 242: const { data: variants, error } = await getVariantsWithProductBusinessId(supabase, variantIds);

app\(dashboard)\dashboard\business\products\actions\deleteProduct.ts
  Line 3: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 4: import { getProductDetailsByIdAndBusinessId, deleteProductByIdAndBusinessId } from "@/lib/supabase/services/businessService";
  Line 5: import { removeFileFromStorage, listStorageFiles } from "@/lib/supabase/services/sharedService";
  Line 8: import { BUCKETS } from "@/lib/supabase/constants";
  Line 16: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 22: const { data: product, error: productError } = await getProductDetailsByIdAndBusinessId(supabase, itemId, user.id);
  Line 49: await removeFileFromStorage(supabase, bucketName, [storagePath]);
  Line 63: const { data: fileList, error: listError } = await listStorageFiles(supabase, bucketName, `${userPath}/products`);
  Line 75: const { data: productFiles, error: productFilesError } = await listStorageFiles(supabase, bucketName, `${userPath}/products/${item.name}`);
  Line 93: const { success: deleteSuccess, error: deleteError } = await removeFileFromStorage(supabase, bucketName, batch);
  Line 108: const { success: deleteSuccess, error: deleteError } = await deleteProductByIdAndBusinessId(supabase, itemId, user.id);

app\(dashboard)\dashboard\business\products\actions\deleteVariant.ts
  Line 3: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 4: import { Tables } from "@/types/supabase";
  Line 5: import { getVariantDetailsWithProductBusinessId, getFilteredProductVariants, deleteProductVariant as deleteProductVariantService, deleteProductVariantsByProductId, getProductDetailsByIdAndBusinessId } from "@/lib/supabase/services/businessService";
  Line 6: import { removeFileFromStorage, listStorageFiles } from "@/lib/supabase/services/sharedService";
  Line 9: import { BUCKETS } from "@/lib/supabase/constants";
  Line 17: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 23: const { data: variantInfo, error: variantError } = await getVariantDetailsWithProductBusinessId(supabase, variantId);
  Line 35: const { data: availableVariants, error: countError } = await getFilteredProductVariants(supabase, variantInfo.product_id, { includeUnavailable: false });
  Line 77: await removeFileFromStorage(supabase, bucketName, [storagePath]);
  Line 93: const { data: variantFiles, error: listError } = await listStorageFiles(supabase, bucketName, variantFolderPath);
  Line 99: await removeFileFromStorage(supabase, bucketName, filesToDelete);
  Line 107: const { success: deleteSuccess, error: deleteError } = await deleteProductVariantService(supabase, variantId);
  Line 136: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 149: const { data: variantInfo, error: variantError } = await getVariantDetailsWithProductBusinessId(supabase, variantId);
  Line 184: const { data: allProductVariants, error: allVariantsError } = await getFilteredProductVariants(supabase, productId);
  Line 231: await removeFileFromStorage(supabase, bucketName, [storagePath]);
  Line 245: const { data: variantFiles } = await listStorageFiles(supabase, bucketName, variantFolderPath);
  Line 249: await removeFileFromStorage(supabase, bucketName, filesToDelete);
  Line 257: const { success: deleteSuccess, error: deleteError } = await deleteProductVariantService(supabase, variant.id);
  Line 292: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 298: const { data: product, error: productError } = await getProductDetailsByIdAndBusinessId(supabase, productId, user.id);
  Line 305: const { data: variants, error: countError } = await getFilteredProductVariants(supabase, productId);
  Line 312: const { success: deleteSuccess, error: deleteError } = await deleteProductVariantsByProductId(supabase, productId);

app\(dashboard)\dashboard\business\products\actions\getProducts.ts
  Line 3: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 4: import { getProductsWithVariantInfo } from "@/lib/supabase/services/businessService";
  Line 22: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 25: const { data, error, count } = await getProductsWithVariantInfo(supabase, user.id, page, limit, filters, sortBy);

app\(dashboard)\dashboard\business\products\actions\getProductWithVariants.ts
  Line 3: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 4: import { getRpcProductWithVariants, getProductBusinessId, getRpcAvailableProductVariants, getFilteredProductVariants, getRpcBusinessVariantStats, getRpcIsVariantCombinationUnique } from "@/lib/supabase/services/businessService";
  Line 21: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 27: const { data: result, error: functionError } = await getRpcProductWithVariants(supabase, productId);
  Line 41: const { data: product, error: ownershipError } = await getProductBusinessId(supabase, productId, user.id);
  Line 80: const { data: variants, error: functionError } = await getRpcAvailableProductVariants(supabase, productId);
  Line 105: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 111: const { data: product, error: ownershipError } = await getProductBusinessId(supabase, productId, user.id);
  Line 118: const { data: variants, error: queryError, count } = await getFilteredProductVariants(supabase, productId, options);
  Line 156: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 162: const { data: stats, error: functionError } = await getRpcBusinessVariantStats(supabase, user.id);
  Line 204: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 210: const { data: product, error: ownershipError } = await getProductBusinessId(supabase, productId, user.id);
  Line 218: supabase,

app\(dashboard)\dashboard\business\products\actions\image-library.ts
  Line 3: import { getAuthenticatedUser, listStorageFiles, getPublicUrlFromStorage } from "@/lib/supabase/services/sharedService";
  Line 4: import { BUCKETS } from "@/lib/supabase/constants";
  Line 41: } = await getAuthenticatedUser(supabase);
  Line 58: const { data: rootItems, error: rootListError } = await listStorageFiles(supabase, bucketName, fullPath, {
  Line 93: const { publicUrl: urlData, error: publicUrlError } = await getPublicUrlFromStorage(supabase, bucketName, filePath);
  Line 144: const { data: folderFiles, error: folderError } = await listStorageFiles(supabase, bucketName, folderPath, { limit: 20 });
  Line 176: const { publicUrl: urlData, error: publicUrlError } = await getPublicUrlFromStorage(supabase, bucketName, filePath);

app\(dashboard)\dashboard\business\products\actions\imageHandlers.ts
  Line 4: import { uploadFileToStorage, getPublicUrlFromStorage, removeFileFromStorage } from "@/lib/supabase/services/sharedService";
  Line 5: import { BUCKETS } from "@/lib/supabase/constants";
  Line 92: const { success: deleteSuccess, error: deleteError } = await removeFileFromStorage(supabase, bucketName, [storagePath]);
  Line 148: supabase,
  Line 161: const { publicUrl: urlData, error: publicUrlError } = await getPublicUrlFromStorage(supabase, bucketName, imagePath);

app\(dashboard)\dashboard\business\products\actions\updateProduct.ts
  Line 3: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 4: import { updateProduct, getProductById } from "@/lib/supabase/services/businessService";
  Line 8: import { COLUMNS } from "@/lib/supabase/constants";
  Line 17: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 127: const { data: currentProduct, error: fetchError } = await getProductById(supabase, itemId);
  Line 180: const { data: updatedProduct, error: updateError } = await updateProduct(supabase, itemId, dataToUpdate);

app\(dashboard)\dashboard\business\products\actions\updateVariant.ts
  Line 3: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 4: import { getVariantDetailsWithProductBusinessId, getRpcIsVariantCombinationUnique, updateProductVariantData } from "@/lib/supabase/services/businessService";
  Line 8: import { COLUMNS } from "@/lib/supabase/constants";
  Line 17: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 23: const { data: existingVariant, error: variantError } = await getVariantDetailsWithProductBusinessId(supabase, variantId);
  Line 88: supabase,
  Line 201: const { data: updatedVariant, error: updateError } = await updateProductVariantData(supabase, variantId, updateData);
  Line 244: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 255: const { data: variant, error: variantError } = await getVariantDetailsWithProductBusinessId(supabase, update.id);
  Line 263: const { data: _updatedVariant, error: updateError } = await updateProductVariantData(supabase, update.id, update.data);

app\(dashboard)\dashboard\business\products\add\page.tsx
  Line 17: } = await supabase.auth.getUser();
  Line 24: const { error: profileError } = await supabase
  Line 36: const { data: subscriptionData, error: subscriptionError } = await supabase
  Line 53: const { count: availableCount, error: availableCountError } = await supabase
  Line 64: const { count: totalCount, error: countError } = await supabase

app\(dashboard)\dashboard\business\products\edit\[productId]\page.tsx
  Line 24: } = await supabase.auth.getUser();
  Line 31: const { data: product, error: productError } = await supabase
  Line 60: const { data: subscriptionData, error: subscriptionError } = await supabase
  Line 77: const { count: availableCount, error: countError } = await supabase
  Line 92: const { data: variantsData, error: variantsError } = await supabase

app\(dashboard)\dashboard\business\products\page.tsx
  Line 20: } = await supabase.auth.getUser();
  Line 30: const { data: subscriptionData, error: subscriptionError } = await supabase

app\(dashboard)\dashboard\business\reviews\page.tsx
  Line 18: } = await supabase.auth.getUser();
  Line 25: const { data: businessProfile, error: profileError } = await supabase
  Line 40: const { count: reviewsReceivedCount } = await supabase
  Line 47: const { count: myReviewsCount } = await supabase

app\(dashboard)\dashboard\business\settings\actions.ts
  Line 4: import { SupabaseClient } from "@supabase/supabase-js";
  Line 5: import { Database } from "@/types/supabase";
  Line 27: } = await supabase.auth.getUser();
  Line 42: const { error: updateError } = await supabase.auth.updateUser({ email });
  Line 68: } = await supabase.auth.getUser();
  Line 75: const { error } = await supabase.auth.signInWithOtp({
  Line 105: } = await supabase.auth.getUser();
  Line 123: const { error: verifyError } = await supabase.auth.verifyOtp({
  Line 167: } = await supabase.auth.getUser();
  Line 199: } = await supabase.auth.getUser();
  Line 209: const { error } = await supabase.auth.signInWithOtp({
  Line 253: const { error } = await supabase.auth.verifyOtp({
  Line 295: } = await supabase.auth.getUser();
  Line 306: const { error } = await supabase.auth.signInWithPassword({
  Line 336: } = await supabase.auth.getUser();
  Line 344: const { data: subscription, error: subscriptionError } = await supabase
  Line 444: const { createClient } = await import('@/utils/supabase/server');
  Line 453: const { data: items, error: listError } = await supabase.storage
  Line 484: const { error: deleteError } = await supabase.storage
  Line 512: const { createClient: _createClient } = await import('@/utils/supabase/server');
  Line 517: const { error: deleteProfileError } = await supabase
  Line 533: await supabase.auth.signOut();
  Line 537: const { error: deleteUserError } = await supabase.auth.admin.deleteUser(user.id, false);
  Line 571: } = await supabase.auth.getUser();
  Line 606: const { error: updateError } = await supabase.auth.updateUser(
  Line 657: } = await supabase.auth.getUser();
  Line 678: const { error: authUpdateError } = await supabase.auth.updateUser(
  Line 745: } = await supabase.auth.getUser();
  Line 768: const { error: verifyError } = await supabase.auth.verifyOtp({
  Line 793: const { error: updateError } = await supabase.auth.updateUser({

app\(dashboard)\dashboard\business\settings\page.tsx
  Line 35: } = await supabase.auth.getUser();

app\(dashboard)\dashboard\business\subscriptions\actions.ts
  Line 71: const { count: totalCount, error: countError } = await supabase
  Line 90: const { data: subscriptions, error: subsError } = await supabase
  Line 115: supabase
  Line 119: supabase
  Line 220: let query = supabase

app\(dashboard)\dashboard\business\subscriptions\page.tsx
  Line 35: } = await supabase.auth.getUser();

app\(dashboard)\dashboard\customer\layout.tsx
  Line 26: } = await supabase.auth.getUser();
  Line 32: const { data: profile, error: profileError } = await supabase

app\(dashboard)\dashboard\customer\likes\actions.ts
  Line 41: let query = supabase
  Line 63: let countQuery = supabase

app\(dashboard)\dashboard\customer\likes\page.tsx
  Line 35: } = await supabase.auth.getUser();

app\(dashboard)\dashboard\customer\overview\page.tsx
  Line 17: } = await supabase.auth.getUser();
  Line 27: const { data: profile, error: profileError } = await supabase
  Line 42: const { count: reviewCount, error: reviewError } = await supabase
  Line 52: const { count: subscriptionCount, error: subscriptionError } = await supabase
  Line 62: const { count: likesCount, error: likesError } = await supabase

app\(dashboard)\dashboard\customer\page.tsx
  Line 17: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 27: const { data: customerProfile, error: profileError } = await supabase
  Line 40: const { data: subscriptions } = await supabase

app\(dashboard)\dashboard\customer\profile\actions.ts
  Line 152: } = await supabase.auth.getUser();
  Line 175: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 204: } = await supabase.auth.getUser();
  Line 229: const { error: updateError } = await supabase
  Line 265: } = await supabase.auth.getUser();
  Line 289: const { error: updateError } = await supabase
  Line 300: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 330: } = await supabase.auth.getUser();
  Line 361: const { data: authData } = await supabase.auth.admin.getUserById(user.id);
  Line 395: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 443: } = await supabase.auth.getUser();
  Line 477: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 525: } = await supabase.auth.getUser();
  Line 563: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 574: const { error: updateError } = await supabase

app\(dashboard)\dashboard\customer\profile\avatar-actions.ts
  Line 16: } = await supabase.auth.getUser();
  Line 47: const { error: uploadError } = await supabase.storage
  Line 63: const { data: urlData } = supabase.storage
  Line 93: } = await supabase.auth.getUser();
  Line 99: const { error: updateError } = await supabase
  Line 130: } = await supabase.auth.getUser();
  Line 144: const { error: deleteError } = await supabase.storage
  Line 157: const { error: updateError } = await supabase

app\(dashboard)\dashboard\customer\profile\page.tsx
  Line 19: } = await supabase.auth.getUser();
  Line 26: const { data: profile, error: profileError } = await supabase

app\(dashboard)\dashboard\customer\reviews\page.tsx
  Line 38: } = await supabase.auth.getUser();
  Line 49: const { count: reviewsCount } = await supabase

app\(dashboard)\dashboard\customer\settings\actions.ts
  Line 22: } = await supabase.auth.getUser();
  Line 57: const { error: updateError } = await supabase.auth.updateUser(
  Line 108: } = await supabase.auth.getUser();
  Line 129: const { error: authUpdateError } = await supabase.auth.updateUser(
  Line 201: } = await supabase.auth.getUser();
  Line 208: const { error } = await supabase.auth.signInWithOtp({
  Line 238: } = await supabase.auth.getUser();
  Line 256: const { error: verifyError } = await supabase.auth.verifyOtp({
  Line 301: } = await supabase.auth.getUser();
  Line 333: } = await supabase.auth.getUser();
  Line 343: const { error } = await supabase.auth.signInWithOtp({
  Line 387: const { error } = await supabase.auth.verifyOtp({
  Line 429: } = await supabase.auth.getUser();
  Line 440: const { error } = await supabase.auth.signInWithPassword({
  Line 476: } = await supabase.auth.getUser();
  Line 491: const { data: items, error: listError } = await supabase.storage
  Line 522: const { error: deleteError } = await supabase.storage
  Line 554: const { error: deleteProfileError } = await supabase
  Line 567: await supabase.auth.signOut();
  Line 571: const { error: deleteUserError } = await supabase.auth.admin.deleteUser(user.id, false);

app\(dashboard)\dashboard\customer\settings\page.tsx
  Line 20: } = await supabase.auth.getUser();

app\(dashboard)\dashboard\customer\subscriptions\actions.ts
  Line 45: let query = supabase

app\(dashboard)\dashboard\customer\subscriptions\page.tsx
  Line 35: } = await supabase.auth.getUser();

app\(main)\actions\getHomepageBusinessCard.ts
  Line 21: } = await supabase.auth.getUser();
  Line 29: const { data: businessProfile, error: profileError } = await supabase
  Line 115: } = await supabase.auth.getUser();
  Line 123: supabase
  Line 128: supabase

app\(main)\auth\callback\AuthCallbackClient.tsx
  Line 22: const supabase = createClient();
  Line 33: const { data: authData, error: authError } = await supabase.auth.getUser();
  Line 169: }, [router, supabase, searchParams]);
  Line 200: const { data: authData, error: authError } = await supabase.auth.getUser();

app\(main)\blog\sitemap.ts
  Line 2: import { createSitemapClient } from "@/utils/supabase/sitemap";
  Line 28: const supabaseClient = createSitemapClient();
  Line 30: const { data: blogs, error } = await supabaseClient

app\(main)\blog\[blogSlug]\page.tsx
  Line 23: const { data: blog, error } = await supabase
  Line 58: let query = supabase

app\(main)\cookies\ModernCookiePolicyClient.tsx
  Line 233: <strong>Supabase:</strong> Used for authentication and database services.

app\(main)\discover\actions\businessActions.ts
  Line 87: } = await supabase.auth.getUser();

app\(main)\discover\actions\combinedActions.ts
  Line 49: } = await supabase.auth.getUser();

app\(main)\discover\actions\locationActions.ts
  Line 8: import { getStateNameByCity, getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 14: } from "@/lib/supabase/services/businessService";
  Line 106: const { stateName, error: stateError } = await getStateNameByCity(supabase, validCity);
  Line 118: const { user } = await getAuthenticatedUser(supabase);
  Line 133: supabase,
  Line 254: supabase,
  Line 303: supabase,
  Line 324: supabase,

app\(main)\discover\actions\productActions.ts
  Line 150: } = await supabase.auth.getUser();
  Line 158: let businessQuery = supabase
  Line 200: let countQuery = supabase
  Line 225: let productsQuery = supabase
  Line 337: } = await supabase.auth.getUser();
  Line 345: const { data: validBusinesses, error: businessError } = await supabase
  Line 373: let countQuery = supabase
  Line 393: let productsQuery = supabase

app\(main)\email-change-success\page.tsx
  Line 18: const supabase = createClient();
  Line 24: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 51: }, [supabase, router]);

app\(main)\LandingPageClient.tsx
  Line 52: const supabase = createClient();
  Line 63: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 76: supabase
  Line 81: supabase
  Line 105: const { data: subscription } = await supabase
  Line 156: }, [supabase]);

app\(main)\login\actions.ts
  Line 3: import { signInWithOtp, verifyOtp, signInWithPassword } from "@/lib/supabase/services/sharedService";
  Line 7: import { handleSupabaseAuthError, isEmailRateLimitError } from "@/lib/utils/supabaseErrorHandler";
  Line 53: const { error } = await signInWithOtp(supabase, email, true);
  Line 66: error: handleSupabaseAuthError(error),
  Line 85: error: handleSupabaseAuthError(error as Error),
  Line 104: const { data, error } = await verifyOtp(supabase, email, otp);
  Line 109: error: handleSupabaseAuthError(error),
  Line 121: error: handleSupabaseAuthError(error as Error),
  Line 144: const { data, error } = await signInWithPassword(supabase, phoneNumber, password);
  Line 149: error: handleSupabaseAuthError(error),
  Line 161: error: handleSupabaseAuthError(error as Error),

app\(main)\login\components\SocialLoginButton.tsx
  Line 7: import { signInWithOAuth } from "@/lib/supabase/services/sharedService";
  Line 25: const supabase = createClient();
  Line 42: const { data, error } = await signInWithOAuth(supabase, provider, callbackUrl, {

app\(main)\login\LoginForm.tsx
  Line 13: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 52: const { user } = await getAuthenticatedUser(supabase);
  Line 112: setCountdown(60); // 60 second countdown (Supabase rate limit)

app\(onboarding)\layout.tsx
  Line 17: const { user } = await getAuthenticatedUser(supabase);

app\(onboarding)\onboarding\actions.ts
  Line 4: import { SupabaseClient } from "@supabase/supabase-js";
  Line 5: import { Database } from "@/types/supabase";
  Line 56: const { data: { user }, error: userError, } = await supabase.auth.getUser();
  Line 65: const { data: profileData, error: profileError } = await getBusinessProfileForOnboarding(supabase, user.id);
  Line 108: const { data: subscriptionData, error: subscriptionError } = await getLatestSubscription(supabase, user.id);
  Line 201: } = await supabase.auth.getUser();
  Line 264: const { exists: existingProfile, error: profileCheckError } = await checkIfBusinessProfileExists(supabase, user.id);
  Line 359: const { data: result, error: rpcError } = await createBusinessProfileAtomic(supabase, profileData, subscriptionData);

app\(onboarding)\onboarding\hooks\useUserData.ts
  Line 11: const supabase = createClient();
  Line 35: const sessionResponse = await supabase.auth.getSession();
  Line 47: }, [router, supabase.auth]);

app\api\admin\fix-subscription-inconsistency\route.ts
  Line 72: const { data: currentSubscription, error: subError } = await supabase
  Line 95: const { data: currentProfile, error: profileError } = await supabase
  Line 156: const { data: fixResult, error: fixError } = await supabase
  Line 184: await supabase
  Line 251: const { data: inconsistencies, error } = await supabase

app\api\business\likes\route.ts
  Line 43: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 58: const adminSupabase = await createClient();
  Line 61: const { count: totalCount, error: countError } = await adminSupabase
  Line 87: const { data: paginatedLikes, error: likesError } = await adminSupabase
  Line 117: adminSupabase
  Line 121: adminSupabase

app\api\business\my-likes\route.ts
  Line 4: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 40: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 56: let countQuery = supabase
  Line 95: let dataQuery = supabase

app\api\business\my-reviews\route.ts
  Line 4: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 36: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 58: let baseQuery = supabase
  Line 114: const { data: businessProfiles } = await supabase

app\api\business\reviews\route.ts
  Line 4: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 5: import { Database } from "@/types/supabase";
  Line 13: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 37: const { data: businessProfile, error: profileError } = await supabase
  Line 51: const { data: requestedProfile, error: requestedProfileError } = await supabase
  Line 68: let baseQuery = supabase
  Line 142: supabase
  Line 146: supabase

app\api\check-user-type\route.ts
  Line 3: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 11: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 21: const { data: businessProfile, error: profileError } = await supabase

app\api\customer\likes\route.ts
  Line 4: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 34: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 50: let countQuery = supabase
  Line 89: let dataQuery = supabase

app\api\customer\reviews\route.ts
  Line 3: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 35: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 54: let baseQuery = supabase
  Line 112: const { data: businessProfiles } = await supabase

app\api\customer\reviews\update\route.ts
  Line 5: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 13: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 42: const { data: reviewData, error: reviewError } = await supabase
  Line 57: const { error: updateError } = await supabase
  Line 79: const { data: businessData } = await supabase

app\api\health\subscription\route.ts
  Line 5: import { TABLES } from "@/lib/supabase/constants";
  Line 93: const { data: subscriptionHealth, error: healthError } = await supabase
  Line 190: const { data: criticalAlerts, error } = await supabase

app\api\razorpay\key\route.ts
  Line 26: } = await supabase.auth.getUser();

app\api\subscription\[id]\payments\route.ts
  Line 4: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 44: } = await supabase.auth.getUser();
  Line 54: const { data: subscription, error: subscriptionError } = await supabase
  Line 77: const { data: businessProfiles } = await supabase
  Line 96: const { data: profile, error: profileError } = await supabase

app\api\subscriptions\list\route.ts
  Line 75: } = await supabase.auth.getUser();

app\api\subscriptions\my\route.ts
  Line 4: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 55: } = await supabase.auth.getUser();
  Line 65: const { data: subscription, error: subscriptionError } = await supabase

app\api\subscriptions\[id]\cancel\route.ts
  Line 4: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 79: } = await supabase.auth.getUser();
  Line 89: const { data: subscription, error: subscriptionError } = await supabase
  Line 151: const { error: updateError } = await supabase
  Line 203: const { error: updateError } = await supabase

app\api\subscriptions\[id]\invoices\route.ts
  Line 4: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 120: } = await supabase.auth.getUser();
  Line 130: const { data: subscription, error: subscriptionError } = await supabase
  Line 150: const { data: businessProfile } = await supabase

app\api\subscriptions\[id]\pause\route.ts
  Line 4: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 79: } = await supabase.auth.getUser();
  Line 89: const { data: subscription, error: subscriptionError } = await supabase
  Line 141: const { error: updateError } = await supabase
  Line 156: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {

app\api\subscriptions\[id]\payments\route.ts
  Line 42: } = await supabase.auth.getUser();
  Line 52: const { data: subscription, error: subscriptionError } = await supabase
  Line 75: const { data: businessProfiles } = await supabase
  Line 94: const { data: profile, error: profileError } = await supabase

app\api\subscriptions\[id]\payments\[paymentId]\route.ts
  Line 39: } = await supabase.auth.getUser();
  Line 49: const { data: subscription, error: subscriptionError } = await supabase
  Line 64: const { data: profile, error: profileError } = await supabase

app\api\subscriptions\[id]\pending-update\route.ts
  Line 4: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 5: import { Database } from "@/types/supabase";
  Line 67: } = await supabase.auth.getUser();
  Line 77: const { data: subscription, error: subscriptionError } = await supabase

app\api\subscriptions\[id]\refund\utils\databaseOperations.ts
  Line 1: import { SupabaseClient } from "@supabase/supabase-js";
  Line 9: _supabase: SupabaseClient,
  Line 33: supabase: SupabaseClient,
  Line 38: const { data: subscription } = await supabase
  Line 50: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
  Line 79: supabase: SupabaseClient,
  Line 84: const { data: subscription } = await supabase
  Line 97: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
  Line 126: _supabase: SupabaseClient,

app\api\subscriptions\[id]\refund\utils\validators.ts
  Line 3: import { SupabaseClient } from "@supabase/supabase-js";
  Line 30: } = await supabase.auth.getUser();
  Line 41: return { valid: true, user, supabase };
  Line 48: supabase: SupabaseClient,
  Line 53: const { data: profile, error: profileError } = await supabase
  Line 82: const { data: subscription, error: subscriptionError } = await supabase

app\api\subscriptions\[id]\resume\route.ts
  Line 74: } = await supabase.auth.getUser();
  Line 84: const { data: subscription, error: subscriptionError } = await supabase
  Line 138: const { error: updateError } = await supabase
  Line 155: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {

app\api\subscriptions\[id]\route.ts
  Line 4: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 5: import { Database } from "@/types/supabase";
  Line 58: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 69: const { data: subscription, error: subscriptionError } = await supabase

app\api\subscriptions\[id]\scheduled-changes\route.ts
  Line 57: } = await supabase.auth.getUser();
  Line 67: const { data: subscription, error: subscriptionError } = await supabase
  Line 178: } = await supabase.auth.getUser();
  Line 188: const { data: subscription, error: subscriptionError } = await supabase

app\api\subscriptions\[id]\switch\route.ts
  Line 54: } = await supabase.auth.getUser();
  Line 64: const { data: subscription, error: subscriptionError } = await supabase

app\api\subscriptions\[id]\switch-with-new-payment\route.ts
  Line 60: } = await supabase.auth.getUser();

app\api\subscriptions\[id]\update\route.ts
  Line 96: } = await supabase.auth.getUser();
  Line 106: const { data: subscription, error: subscriptionError } = await supabase
  Line 245: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {

app\api\test\subscription-flow\route.ts
  Line 35: const { data: { user }, error: authError } = await supabase.auth.getUser();

app\api\test\subscription-scenarios\route.ts
  Line 36: const { data: { user }, error: authError } = await supabase.auth.getUser();

app\api\webhooks\razorpay\retry\route.ts
  Line 133: const { data: stats, error } = await supabase

app\auth\actions.ts
  Line 3: import { signOutUser } from "@/lib/supabase/services/sharedService";
  Line 11: const { error: _error } = await signOutUser(supabase);

app\components\BottomNav.tsx
  Line 128: const supabase = createClient();
  Line 129: const { data: { user } } = await supabase.auth.getUser();
  Line 133: const { data: businessProfile } = await supabase
  Line 144: const { data: customerProfile } = await supabase

app\components\Header.tsx
  Line 17: import type { User } from "@supabase/supabase-js";
  Line 39: const supabase = createClient();
  Line 44: } = await supabase.auth.getUser();
  Line 49: supabase
  Line 54: supabase

app\locality\actions\businessActions.ts
  Line 69: let query = supabase
  Line 171: let query = supabase

app\locality\actions\combinedActions.ts
  Line 41: const { data: { session } } = await supabase.auth.getSession();

app\locality\actions\locationActions.ts
  Line 43: const { data: { session }, } = await supabase.auth.getSession();

app\locality\actions\productActions.ts
  Line 40: const businessQuery = supabase
  Line 69: let productQuery = supabase

app\[cardSlug]\actions.ts
  Line 3: import { getProductsWithFiltersAndPagination, getBusinessProfileStatus, insertCardVisit } from "@/lib/supabase/services/businessService";
  Line 7: import { COLUMNS, TABLES } from "@/lib/supabase/constants";
  Line 8: import { Tables } from "@/types/supabase";
  Line 47: supabase,
  Line 125: const { status, error: profileError } = await getBusinessProfileStatus(supabase, businessProfileId);
  Line 135: const { success: insertSuccess, error: insertError } = await insertCardVisit(supabase, {

app\[cardSlug]\gallery\page.tsx
  Line 78: } = await supabase.auth.getUser();
  Line 83: const { data: subscription } = await supabase

app\[cardSlug]\page.tsx
  Line 1: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 2: import { updateBusinessProfile, getSecureBusinessProfileBySlug, getAdDataForPincode, getProductsForBusiness, getReviewsCountForBusiness } from "@/lib/supabase/services/businessService";
  Line 3: import { TABLES, COLUMNS } from "@/lib/supabase/constants";
  Line 4: import { Tables } from "@/types/supabase";
  Line 66: await getSecureBusinessProfileBySlug(supabase, cardSlug);
  Line 110: const { error: updateError } = await updateBusinessProfile(supabase, businessProfile.id, {
  Line 132: const { adData, error: adError } = await getAdDataForPincode(supabase, pincode);
  Line 153: const { products: initialProducts, count: totalProductCount, error: productsError } = await getProductsForBusiness(supabase, businessProfile.id, INITIAL_PRODUCTS_PAGE_SIZE);
  Line 162: const { user } = await getAuthenticatedUser(supabase);
  Line 166: const { count: totalReviews, error: reviewsCountError } = await getReviewsCountForBusiness(supabase, businessProfile.id);
  Line 256: await getSecureBusinessProfileBySlug(supabase, cardSlug);

app\[cardSlug]\product\actions.ts
  Line 28: const { data, error } = await supabase
  Line 99: const { data, error } = await supabase
  Line 176: const { data: productData, error: productError } = await supabase
  Line 217: const { data: variantsData, error: variantsError } = await supabase
  Line 291: const { data, error } = await supabase
  Line 380: const { data: validBusinesses, error: businessError } = await supabase
  Line 400: const { data, error } = await supabase
  Line 511: const { data, error } = await supabase

app\[cardSlug]\product\[productSlug]\page.tsx
  Line 114: const { count, error: tableCheckError } = await supabase
  Line 122: const { data: adData, error: adError } = await supabase.rpc(
  Line 143: const { data: customAd } = await supabase
  Line 166: const { data: globalAd } = await supabase

components\feed\shared\forms\LocationDisplay.tsx
  Line 31: const supabase = createClient();
  Line 32: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 40: const { data: businessProfile } = await supabase
  Line 58: const { data: customerProfile } = await supabase

components\feed\shared\hooks\usePostOwnership.ts
  Line 27: const supabase = createClient();
  Line 28: const { data: { user }, error } = await supabase.auth.getUser();

components\feed\shared\ModernCustomerPostCard.tsx
  Line 64: const supabase = createClient();
  Line 65: const { data: { user } } = await supabase.auth.getUser();

components\feed\shared\SocialMediaBusinessPostCreator.tsx
  Line 212: const supabase = createClient();
  Line 213: const { data: { user } } = await supabase.auth.getUser();
  Line 216: const { data: profile } = await supabase
  Line 453: const supabase = createClient();
  Line 454: const { data: { user } } = await supabase.auth.getUser();
  Line 461: const { data: profile, error } = await supabase

components\feed\shared\SocialMediaPostCreator.tsx
  Line 51: const supabase = createClient();
  Line 52: const { data: { user } } = await supabase.auth.getUser();
  Line 55: const { data: profile } = await supabase
  Line 158: const supabase = createClient();
  Line 159: const { data: { user } } = await supabase.auth.getUser();
  Line 166: const { data: profile, error } = await supabase

components\post\ConditionalPostLayout.tsx
  Line 5: import { User } from '@supabase/supabase-js';
  Line 35: const supabase = createClient();
  Line 40: const { data: { user } } = await supabase.auth.getUser();
  Line 45: const { data: businessProfile } = await supabase
  Line 59: const { data: customerProfile } = await supabase
  Line 81: }, [supabase]);

components\sidebar\BusinessAppSidebar.tsx
  Line 96: const supabase = createClient();
  Line 101: } = await supabase.auth.getUser();

dependency-graph.json
  Line 39731: "module": "@/lib/utils/supabaseErrorHandler",
  Line 39741: "resolved": "lib/utils/supabaseErrorHandler.ts",
  Line 39757: "source": "lib/utils/supabaseErrorHandler.ts",

docs\brief.md
  Line 123: - **Backend:** Supabase (Database, Authentication, and other backend services)
  Line 124: - **Database:** Supabase (PostgreSQL)
  Line 125: - **Hosting/Infrastructure:** Cloud Supabase (Backend), Google Cloud Run (Frontend Hosting)
  Line 144: - **Budget:** Self-funded, with limited financial resources. Currently utilizing a **Redis free plan** and a **Supabase Pro plan**. Operational costs are a significant consideration, and careful management is required.
  Line 154: - **Supabase Scalability:** Supabase's Pro plan will adequately support initial user growth and transaction volumes without significant performance bottlenecks or unexpected costs.
  Line 165: - **Supabase Cost Escalation:** While currently on a Pro plan, unexpected increases in usage or changes in Supabase pricing could lead to unsustainable costs.
  Line 180: - **Scalability Beyond Supabase:** At what point will the current Supabase Pro plan become a bottleneck, and what are the alternative backend solutions or architectural changes required for significant scaling?
  Line 198: (To be populated with relevant links and documents, e.g., links to Supabase documentation, Razorpay API docs, etc.)

docs\brownfield-architecture.md
  Line 33: | Backend | Supabase | ^2.50.0 | Database, Auth, and other backend services |
  Line 68: │   ├── client/        # Client-side Supabase logic
  Line 79: - **`lib/actions`**: This is the core of the application's backend logic. It contains server-side actions organized by feature. This is where most of the business logic, data manipulation, and interactions with Supabase occur.
  Line 82: - `realtimeService.ts`: Likely handles real-time updates using Supabase Realtime.
  Line 87: - **`lib/client`**: Handles client-side interactions with Supabase.
  Line 113: - **Supabase:** Used for database, authentication, and other backend services.
  Line 126: | Supabase | Backend Platform | SDK | `lib/client/`, `utils/supabase/` |

docs\supabase-docs\ad-implementation.md
  Line 9: 1. **Custom Ads**: Targeted ads based on pincode, managed through Supabase
  Line 248: Custom ads are managed through the Supabase database. See the [Custom Ads Management](./custom-ads-management.md) document for details.

docs\supabase-docs\cron-jobs.md
  Line 1: # Supabase Cron Jobs Documentation
  Line 3: This document provides an overview of all the cron jobs running in the Dukancard Supabase project. These scheduled tasks automate various maintenance and data processing operations.
  Line 37: - **Implementation**: Runs entirely on Supabase using `SELECT check_and_process_expired_trials();` - no application-level code needed

docs\supabase-docs\custom-ads-management.md
  Line 323: const { data: adData, error: adError } = await supabase.rpc(

docs\supabase-docs\custom-ads-technical-implementation.md
  Line 615: const { count, error: tableCheckError } = await supabase
  Line 623: const { data: adData, error: adError } = await supabase.rpc(

docs\supabase-docs\function-implementations.md
  Line 1: # Supabase Function Implementations
  Line 3: This document provides the actual SQL implementations of key functions in the Dukancard Supabase project. These implementations can serve as reference for understanding the database logic in detail.

docs\supabase-docs\monitoring-implementation.md
  Line 11: 1. **Database Functions** - Core monitoring functions in Supabase
  Line 217: ### Location: `utils/supabase/middleware.ts`
  Line 296: - [ ] Database functions created in Supabase

docs\supabase-docs\supabase\ad_targets_view.md
  Line 59: This view follows Supabase security best practices:

docs\supabase-docs\supabase\business_posts.md
  Line 112: - **Query Structure**: Uses Supabase `.or()` method to combine multiple conditions

docs\supabase-docs\supabase\customer_profiles.md
  Line 188: - The actual image file is likely stored in Supabase Storage or another storage service.
  Line 232: - All updates to email, phone, and name should be made through `auth.users` table using Supabase Auth methods.

docs\supabase-docs\supabase\database_views_index.md
  Line 47: - Views follow Supabase security guidelines (no SECURITY DEFINER)

docs\supabase-docs\supabase\expired_ads_view.md
  Line 54: This view follows Supabase security best practices:

docs\supabase-docs\supabase\system_alerts.md
  Line 103: import { createAdminClient } from "@/utils/supabase/admin";

docs\supabase-docs\supabase\unified_posts_view.md
  Line 95: This view follows Supabase security best practices:
  Line 222: ### Supabase Client Usage
  Line 223: - **Client-side queries**: Uses normal Supabase client (`createClient()`) for all feed operations
  Line 224: - **No server load**: Queries run directly from browser to Supabase, reducing server load

docs\supabase-docs\testing\subscription-flow-manual-testing.md
  Line 9: 3. Access to Supabase admin panel for database verification
  Line 266: 1. Check Supabase logs for database errors

e2e\utils\auth-helpers.ts
  Line 100: export async function mockSupabaseResponses(page: Page, authState: TestAuthState) {
  Line 160: await mockSupabaseResponses(page, authState);

jest.config.ts
  Line 12: "/node_modules/(?!(@supabase/ssr|@supabase/auth-helpers-nextjs|@supabase/realtime-js|@supabase/supabase-js|@radix-ui|@dnd-kit|@testing-library|framer-motion|lucide-react|sonner|next-intl|use-intl|uncrypto|@upstash|uuid|@upstash/redis)/)",
  Line 24: '^@/utils/supabase/client$': '<rootDir>/__mocks__/utils/supabase/client.ts',
  Line 29: '^@supabase/ssr$': '<rootDir>/__mocks__/supabase-ssr.js',
  Line 30: '^@supabase/auth-helpers-nextjs$': '<rootDir>/__mocks__/supabase-auth-helpers-nextjs.js',
  Line 31: '^@supabase/realtime-js$': '<rootDir>/__mocks__/supabase-realtime.js',
  Line 32: '^@supabase/supabase-js$': '<rootDir>/__mocks__/supabase-js.js',

jest.setup.ts
  Line 123: jest.mock('@/utils/supabase/client', () => ({

knowledge_base\technical\api-endpoints.md
  Line 3: This document outlines the API endpoints implemented in the Dukancard web application, primarily located within the `app/api` directory. These endpoints act as an intermediary layer between the frontend and Supabase or external services like Razorpay.
  Line 7: The Dukancard project utilizes Next.js API Routes to expose various backend functionalities. These routes handle requests from the frontend, interact with Supabase (for database operations and authentication), and integrate with external services. The API design generally follows RESTful principles.

knowledge_base\technical\external-integrations.md
  Line 5: ## 1. Supabase API

lib\actions\activities.ts
  Line 5: import { Tables } from "@/types/supabase";
  Line 259: } = await supabase.auth.getUser();
  Line 276: let query = supabase
  Line 324: supabase
  Line 328: supabase
  Line 377: const { error: markError } = await supabase
  Line 426: } = await supabase.auth.getUser();
  Line 441: const { error } = await supabase
  Line 459: const { data: unreadActivities, error: fetchError } = await supabase
  Line 481: const { error: updateError } = await supabase
  Line 521: } = await supabase.auth.getUser();
  Line 533: const { count, error } = await supabase

lib\actions\blogs.ts
  Line 4: import { Tables } from "@/types/supabase";
  Line 45: let supabaseQuery = supabase
  Line 66: supabaseQuery = supabaseQuery.or(
  Line 72: supabaseQuery = supabaseQuery.order("published_at", {
  Line 79: supabaseQuery = supabaseQuery.range(from, to);
  Line 81: const { data, error, count } = await supabaseQuery;
  Line 127: const { data, error } = await supabase

lib\actions\businessProfiles\access.ts
  Line 18: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 25: const { data, error } = await supabase
  Line 55: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 62: const { data, error } = await supabase

lib\actions\businessProfiles\discovery.ts
  Line 34: const countQuery = supabase
  Line 68: let businessQuery = supabase
  Line 177: let validBusinessQuery = supabase

lib\actions\businessProfiles\location.ts
  Line 71: let query = supabase

lib\actions\businessProfiles\profileRetrieval.ts
  Line 3: import { getSecureBusinessProfileBySlug as getSecureBusinessProfileBySlugService, getSecureBusinessProfileWithProductsBySlug as getSecureBusinessProfileWithProductsBySlugService } from "@/lib/supabase/services/businessService";
  Line 24: const { data: profileData, error: profileError } = await getSecureBusinessProfileBySlugService(supabase, slug);
  Line 65: const { data: profileData, error: profileError } = await getSecureBusinessProfileWithProductsBySlugService(supabase, slug);

lib\actions\businessProfiles\search.ts
  Line 10: import { Tables } from "@/types/supabase";
  Line 46: let countQuery = supabase
  Line 51: let businessQuery = supabase
  Line 127: await supabase

lib\actions\businessProfiles\sitemap.ts
  Line 5: import { Tables } from "@/types/supabase";
  Line 20: const { data: profiles, error: profilesError } = await supabase

lib\actions\businessProfiles\types.ts
  Line 1: import { Tables } from "@/types/supabase";

lib\actions\categories\locationBasedFetching.ts
  Line 61: let query = supabase
  Line 174: let businessQuery = supabase
  Line 227: let productQuery = supabase

lib\actions\customerPosts\crud.ts
  Line 18: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 29: const { data: customerProfile, error: profileError } = await supabase
  Line 56: const { data, error } = await supabase
  Line 85: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 96: const { data: existingPost, error: postError } = await supabase
  Line 120: const { data, error } = await supabase
  Line 150: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 161: const { data: existingPost, error: postError } = await supabase
  Line 200: const { error } = await supabase
  Line 226: let query = supabase
  Line 279: const { data, error } = await supabase

lib\actions\customerProfiles\addressValidation.ts
  Line 21: const { data: profile, error } = await supabase
  Line 97: const { data: profile, error } = await supabase
  Line 176: const { data: profile, error } = await supabase

lib\actions\gallery.ts
  Line 3: import { getBusinessProfileGallery, getBusinessProfileIdAndStatusBySlug, getPublicSubscriptionStatus } from "@/lib/supabase/services/businessService";
  Line 4: import { SupabaseClient } from "@supabase/supabase-js";
  Line 5: import { Database } from "@/types/supabase";
  Line 7: import { Tables } from "@/types/supabase";
  Line 26: const { data: galleryData, error } = await getBusinessProfileGallery(supabase, businessId);
  Line 71: const { data: business, error: businessError } = await getBusinessProfileIdAndStatusBySlug(supabase, businessSlug);
  Line 82: const { planId, error: subscriptionError } = await getPublicSubscriptionStatus(supabase, business.id);
  Line 92: const { data: galleryData, error: galleryError } = await getBusinessProfileGallery(supabase, business.id);
  Line 144: const { data: business, error: businessError } = await getBusinessProfileIdAndStatusBySlug(supabase, businessSlug);
  Line 155: const { planId, error: subscriptionError } = await getPublicSubscriptionStatus(supabase, business.id);
  Line 165: const { data: galleryData, error: galleryError } = await getBusinessProfileGallery(supabase, business.id);
  Line 220: const { data: business, error: businessError } = await getBusinessProfileIdAndStatusBySlug(supabase, businessSlug);
  Line 247: const { planId, error: subscriptionError } = await getPublicSubscriptionStatus(supabase, business.id);
  Line 257: const { data: galleryData, error: galleryError } = await getBusinessProfileGallery(supabase, business.id);

lib\actions\interactions.ts
  Line 18: } = await supabase.auth.getUser();
  Line 33: const { data: userBusinessProfile } = await supabase
  Line 41: const { error: insertError } = await supabase
  Line 65: const { data: cardData } = await supabase
  Line 105: } = await supabase.auth.getUser();
  Line 120: const { data: userBusinessProfile } = await supabase
  Line 128: const { error: deleteError } = await supabase
  Line 143: const { data: cardData } = await supabase
  Line 183: } = await supabase.auth.getUser();
  Line 200: const { error: upsertError } = await supabase
  Line 224: const { data: cardData } = await supabase
  Line 256: } = await supabase.auth.getUser();
  Line 264: const { error: deleteError } = await supabase
  Line 278: const { data: cardData } = await supabase
  Line 310: } = await supabase.auth.getUser();
  Line 323: const { error: insertError } = await supabase
  Line 345: const { data: cardData } = await supabase
  Line 356: const { data: userBusinessProfile } = await supabase
  Line 388: } = await supabase.auth.getUser();
  Line 404: const { error: deleteError } = await supabase
  Line 419: const { data: cardData } = await supabase
  Line 430: const { data: userBusinessProfile } = await supabase
  Line 467: } = await supabase.auth.getUser();
  Line 488: supabase
  Line 492: supabase
  Line 496: supabase

lib\actions\location\locationBySlug.ts
  Line 34: const { data, error } = await supabase

lib\actions\location.ts
  Line 4: import { SupabaseClient } from "@supabase/supabase-js";
  Line 5: import { Database } from "@/types/supabase";
  Line 27: const { data: pincodeData, error: pincodeError } = await supabase
  Line 85: const { data: cityData, error: cityError } = await supabase
  Line 159: const supabase = (await createClient()) as SupabaseClient<Database>;
  Line 162: const { data: cityData, error: cityError } = await supabase
  Line 174: const { data: fallbackData, error: fallbackError } = await supabase

lib\actions\posts\crud.ts
  Line 16: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 27: const { data: businessProfile, error: profileError } = await supabase
  Line 56: const { data, error } = await supabase
  Line 89: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 100: const { data: existingPost, error: postError } = await supabase
  Line 116: const { data, error } = await supabase
  Line 154: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 165: const { data: existingPost, error: postError } = await supabase
  Line 181: const { data, error } = await supabase
  Line 219: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 230: const { data: existingPost, error: postError } = await supabase
  Line 255: const { data, error } = await supabase
  Line 290: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 301: const { data: existingPost, error: postError } = await supabase
  Line 338: const { error } = await supabase

lib\actions\posts\fetchSinglePost.ts
  Line 32: const { data, error } = await supabase

lib\actions\posts\types.ts
  Line 6: import type { SupabaseClient as BaseSupabaseClient } from '@supabase/supabase-js';
  Line 7: import type { User } from '@supabase/supabase-js';
  Line 26: export type SupabaseClient = BaseSupabaseClient;

lib\actions\posts\unifiedFeed.ts
  Line 1: import { TABLES, COLUMNS } from "../../supabase/constants";
  Line 2: import { Database, Tables } from "../../../types/supabase";
  Line 3: import { getAuthenticatedUser, fetchUserSubscriptions, getUnifiedPosts } from "@/lib/supabase/services/sharedService";
  Line 4: import { getCustomerProfileLocation } from "@/lib/supabase/services/customerService";
  Line 5: import { getBusinessProfileLocation } from "@/lib/supabase/services/businessService";
  Line 73: const { user, error: authError } = await getAuthenticatedUser(supabase);
  Line 97: const { data: subscriptions } = await fetchUserSubscriptions(supabase, user.id);
  Line 103: getCustomerProfileLocation(supabase, user.id),
  Line 104: getBusinessProfileLocation(supabase, user.id)
  Line 139: const { data: subscriptions } = await fetchUserSubscriptions(supabase, user.id);
  Line 167: getCustomerProfileLocation(supabase, user.id),
  Line 168: getBusinessProfileLocation(supabase, user.id)
  Line 183: getCustomerProfileLocation(supabase, user.id),
  Line 184: getBusinessProfileLocation(supabase, user.id)
  Line 199: getCustomerProfileLocation(supabase, user.id),
  Line 200: getBusinessProfileLocation(supabase, user.id)
  Line 215: getCustomerProfileLocation(supabase, user.id),
  Line 216: getBusinessProfileLocation(supabase, user.id)
  Line 236: const { data, error, count } = await getUnifiedPosts(supabase, from, to, conditions);

lib\actions\products\fetchProductsByIds.ts
  Line 4: import { Tables } from "@/types/supabase";
  Line 33: const { data, error } = await supabase

lib\actions\products\sitemapHelpers.ts
  Line 25: const { data: businessProfiles, error: businessError } = await supabase
  Line 45: const { data: products, error: productsError } = await supabase

lib\actions\redirectAfterLogin.ts
  Line 7: import { SupabaseClient } from '@supabase/supabase-js';
  Line 8: import { Database } from '../../types/supabase';
  Line 9: import { checkIfCustomerProfileExists } from "@/lib/supabase/services/customerService";
  Line 10: import { checkIfBusinessProfileExists } from "@/lib/supabase/services/businessService";
  Line 20: checkIfCustomerProfileExists(supabase, userId),
  Line 21: checkIfBusinessProfileExists(supabase, userId),
  Line 25: console.error("[redirectAfterLogin] Supabase query error:", customerRes.error, businessRes.error);

lib\actions\reviews.ts
  Line 8: import { Tables } from "@/types/supabase";
  Line 45: let query = supabase
  Line 109: const { data: businessSlugs } = await supabase

lib\actions\secureCustomerProfiles.ts
  Line 4: import { getBusinessProfileById } from '@/lib/supabase/services/businessService';
  Line 5: import { checkIfCustomerProfileExists, getPublicCustomerProfileById, getPublicCustomerProfilesByIds } from '@/lib/supabase/services/customerService';
  Line 33: const { data, error } = await getPublicCustomerProfileById(supabase, userId);
  Line 75: const { data, error } = await getPublicCustomerProfilesByIds(supabase, userIds);
  Line 104: const { data, error } = await getPublicCustomerProfilesByIds(supabase, []);
  Line 139: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 149: const { exists, error } = await checkIfCustomerProfileExists(supabase, userId);
  Line 184: const { data: customerProfile, error: customerError } = await getPublicCustomerProfileById(supabase, userId);
  Line 201: const { data: businessProfile, error: businessError } = await getBusinessProfileById(supabase, userId);
  Line 250: const { data: customerProfiles, error: customerError } = await supabase
  Line 261: const { data: businessProfiles, error: businessError } = await supabase

lib\actions\shared\delete-customer-post-media.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 3: import { Database } from "@/types/supabase";
  Line 5: import { BUCKETS } from "../../supabase/constants";
  Line 17: const adminSupabase = await createClient() as SupabaseClient<Database>;
  Line 24: const { data: files, error: listError } = await adminSupabase.storage
  Line 49: const { error: deleteError } = await adminSupabase.storage

lib\actions\shared\productActions.ts
  Line 5: import { Tables } from "@/types/supabase";
  Line 29: } = await supabase.auth.getUser();
  Line 39: const adminSupabase = await createClient();
  Line 42: const { data, error } = await adminSupabase
  Line 92: } = await supabase.auth.getUser();
  Line 103: const { data, error } = await supabase

lib\actions\shared\upload-business-post-media.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 3: import { Database } from "@/types/supabase";
  Line 28: } = await supabase.auth.getUser();
  Line 69: const { data: businessProfile, error: profileError } = await supabase
  Line 92: const adminSupabase = await createClient() as SupabaseClient<Database>;
  Line 95: const { error: uploadError } = await adminSupabase.storage
  Line 111: const { data: urlData } = adminSupabase.storage

lib\actions\shared\upload-customer-post-media.ts
  Line 4: import { SupabaseClient } from "@supabase/supabase-js";
  Line 5: import { Database } from "@/types/supabase";
  Line 30: } = await supabase.auth.getUser();
  Line 62: const { data: existingPost, error: postError } = await supabase
  Line 86: const adminSupabase = await createClient() as SupabaseClient<Database>;
  Line 89: const { error: uploadError } = await adminSupabase.storage
  Line 105: const { data: urlData } = adminSupabase.storage

lib\actions\shared\upload-post-media.ts
  Line 4: import { SupabaseClient } from "@supabase/supabase-js";
  Line 5: import { Database } from "@/types/supabase";
  Line 30: } = await supabase.auth.getUser();
  Line 94: const adminSupabase = createClient();
  Line 97: const client = await adminSupabase;
  Line 149: const adminSupabase = await createClient() as SupabaseClient<Database>;
  Line 156: const { data: files, error: listError } = await adminSupabase.storage
  Line 187: const { error: deleteError } = await adminSupabase.storage

lib\actions\subscription\activateTrial.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 3: import { Database } from "@/types/supabase";
  Line 44: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 52: const { data: profile, error: profileError } = await supabase
  Line 98: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {

lib\actions\subscription\centralized.ts
  Line 30: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 112: const { data: profile, error: profileError } = await supabase
  Line 124: const { data: subscription, error: subscriptionError } = await supabase

lib\actions\subscription\confirm.ts
  Line 4: import { SupabaseClient } from "@supabase/supabase-js";
  Line 5: import { Database } from "@/types/supabase";
  Line 25: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 33: const { data: profile, error: profileError } = await supabase
  Line 45: const { data: existingSubscriptionData } = await supabase
  Line 54: const { data: newSubscriptionCheck } = await supabase
  Line 128: const { data: subscription, error: subscriptionError } = await supabase
  Line 135: const adminSupabase = await createClient();
  Line 197: const { data: existingBusinessSubscription, error: existingBusinessError } = await supabase
  Line 228: const { data: existingSubDetails, error: existingSubError } = await supabase
  Line 287: const { error: updateError } = await supabase
  Line 310: const { error: insertError } = await supabase
  Line 322: const { data: raceSubscription, error: raceError } = await supabase
  Line 334: const { error: raceUpdateError } = await supabase
  Line 409: const { data: atomicResult, error: atomicError } = await adminSupabase.rpc('update_subscription_atomic', {
  Line 425: const { data: currentSubscription, error: currentSubscriptionError } = await supabase
  Line 444: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {

lib\actions\subscription\create.ts
  Line 40: const supabase = await import("@/utils/supabase/server").then((mod) =>
  Line 46: } = await supabase.auth.getUser();
  Line 52: const { data: profile, error: profileError } = await supabase
  Line 162: const supabase = await import("@/utils/supabase/server").then((mod) =>
  Line 166: await supabase
  Line 276: const supabase = await import("@/utils/supabase/server").then((mod) =>
  Line 279: const { data: subscription, error: subscriptionError } = await supabase
  Line 297: const { data: profile, error: profileError } = await supabase

lib\actions\subscription\manage\cancel.ts
  Line 29: const supabase = await import("@/utils/supabase/server").then(mod => mod.createClient());
  Line 32: const { data: activeSubscription, error: activeSubscriptionError } = await supabase
  Line 50: const { data: authSubscription, error: authSubscriptionError } = await supabase
  Line 92: const { error: updateError } = await supabase
  Line 155: const { error: updateError } = await supabase

lib\actions\subscription\manage\change.ts
  Line 37: const supabase = await import("@/utils/supabase/server").then(mod => mod.createClient());
  Line 44: const { data: subscription, error: subscriptionError } = await supabase
  Line 151: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
  Line 212: const supabase = await import("@/utils/supabase/server").then(mod => mod.createClient());
  Line 213: const { data: subscription, error: subscriptionError } = await supabase

lib\actions\subscription\manage\manage.ts
  Line 30: const supabase = await import("@/utils/supabase/server").then(mod => mod.createClient());
  Line 33: const { data: activeSubscription, error: activeSubscriptionError } = await supabase
  Line 51: const { data: authSubscription, error: authSubscriptionError } = await supabase
  Line 106: const { error: updateError } = await supabase
  Line 146: const { error: updateError } = await supabase

lib\actions\subscription\manage\schedule.ts
  Line 35: const supabase = await import("@/utils/supabase/server").then(mod => mod.createClient());
  Line 36: const { data: subscription, error: subscriptionError } = await supabase
  Line 62: const { error: updateError } = await supabase

lib\actions\subscription\manage\switch.ts
  Line 41: const supabase = await import("@/utils/supabase/server").then((mod) =>
  Line 44: const { data: subscription, error: subscriptionError } = await supabase
  Line 148: const { data: _businessProfile } = await supabase
  Line 201: const { data: profileData } = await supabase
  Line 303: const supabase = await import("@/utils/supabase/server").then((mod) =>
  Line 306: const { data: subscription, error: subscriptionError } = await supabase
  Line 396: const supabase = await import("@/utils/supabase/server").then((mod) =>
  Line 399: const { data: subscription, error: subscriptionError } = await supabase
  Line 480: const { data: profile } = await supabase
  Line 533: const { data: profileData } = await supabase

lib\actions\subscription\status.ts
  Line 4: import { SupabaseClient } from "@supabase/supabase-js";
  Line 5: import { Database } from "@/types/supabase";
  Line 44: const { data: subscriptions, error: subscriptionError } = await supabase
  Line 70: const { data: currentSubscription, error: fetchError } = await supabase
  Line 87: const { error: updateError } = await supabase
  Line 113: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
  Line 167: const { data: subscriptions, error: subscriptionError } = await supabase
  Line 205: const { error: restoreError } = await supabase
  Line 228: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
  Line 298: const { data: subscriptions, error: subscriptionError } = await supabase

lib\actions\subscription\utils.ts
  Line 6: import { Tables } from "@/types/supabase";
  Line 21: const { data, error } = await supabase.auth.getUser();
  Line 43: const { count, error: countError } = await supabase
  Line 65: const { data, error } = await supabase
  Line 132: const { data, error } = await supabase
  Line 347: const { data, error } = await supabase

lib\client\locationUtils.ts
  Line 41: const supabase = createClient();
  Line 44: const { data: cityData, error: cityError } = await supabase
  Line 56: const { data: fallbackData, error: fallbackError } = await supabase
  Line 115: const supabase = createClient();
  Line 118: const { data: pincodeData, error: pincodeError } = await supabase
  Line 176: const supabase = createClient();
  Line 180: const { data: cityData, error: cityError } = await supabase
  Line 193: const { data: fallbackData, error: fallbackError } = await supabase

lib\razorpay\webhooks\errorTracking.ts
  Line 3: import { SupabaseClient } from "@supabase/supabase-js";
  Line 35: supabase?: SupabaseClient
  Line 39: const client = supabase || await createClient();
  Line 87: supabase?: SupabaseClient
  Line 91: const client = supabase || await createClient();
  Line 135: supabase?: SupabaseClient
  Line 139: const client = supabase || await createClient();

lib\razorpay\webhooks\handlers\core\eventManager.ts
  Line 1: import { SupabaseClient } from "@supabase/supabase-js";
  Line 5: private supabase: SupabaseClient;
  Line 7: private constructor(supabaseClient: SupabaseClient) {
  Line 8: this.supabase = supabaseClient;
  Line 12: const supabaseClient = await createClient();
  Line 13: return new EventManager(supabaseClient);
  Line 17: const { data, error } = await this.supabase
  Line 33: const { error } = await this.supabase
  Line 49: const { error } = await this.supabase
  Line 65: const { error } = await this.supabase

lib\razorpay\webhooks\handlers\core\subscriptionManager.ts
  Line 1: import { SupabaseClient } from "@supabase/supabase-js";
  Line 8: private supabase: SupabaseClient;
  Line 10: private constructor(supabaseClient: SupabaseClient) {
  Line 11: this.supabase = supabaseClient;
  Line 15: const supabaseClient = await createClient();
  Line 16: return new SubscriptionManager(supabaseClient);
  Line 20: const { data, error } = await this.supabase
  Line 50: const { data: businessProfile, error: profileError } = await this.supabase
  Line 71: const { data: newSubscription, error: createError } = await this.supabase
  Line 119: const { data: result, error } = await this.supabase.rpc('update_subscription_atomic', {

lib\razorpay\webhooks\handlers\mainHandler.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 146: const supabase: SupabaseClient = await createClient();
  Line 155: supabase
  Line 173: result = await handleSubscriptionAuthenticated(payload, supabase, razorpayEventId);
  Line 177: result = await handleSubscriptionActivated(payload, supabase, razorpayEventId);
  Line 181: result = await handleSubscriptionCharged(payload, supabase, razorpayEventId);
  Line 185: result = await handleSubscriptionPending(payload, supabase, razorpayEventId);
  Line 189: result = await handleSubscriptionHalted(payload, supabase, razorpayEventId);
  Line 193: result = await handleSubscriptionCancelled(payload, supabase, razorpayEventId);
  Line 197: result = await handleSubscriptionCompleted(payload, supabase, razorpayEventId);
  Line 201: result = await handleSubscriptionExpired(payload, supabase, razorpayEventId);
  Line 205: result = await handleSubscriptionUpdated(payload, supabase, razorpayEventId);
  Line 210: result = await handlePaymentAuthorized(payload, supabase, razorpayEventId);
  Line 214: result = await handlePaymentCaptured(payload, supabase, razorpayEventId);
  Line 228: result = await handleRefundCreated(payload, supabase, razorpayEventId);
  Line 232: result = await handleRefundProcessed(payload, supabase, razorpayEventId);
  Line 236: result = await handleRefundFailed(payload, supabase, razorpayEventId);
  Line 289: supabase

lib\razorpay\webhooks\handlers\paymentHandlers.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 3: import { SupabaseSubscriptionStatus } from "../types";
  Line 19: _supabase: SupabaseClient,
  Line 113: SupabaseSubscriptionStatus._AUTHENTICATED,
  Line 153: _supabase: SupabaseClient,
  Line 216: SupabaseSubscriptionStatus._ACTIVE, // Payment captured means subscription is active

lib\razorpay\webhooks\handlers\refundHandlers.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 18: _supabase: SupabaseClient,
  Line 70: _supabase: SupabaseClient,
  Line 116: _supabase: SupabaseClient,

lib\razorpay\webhooks\handlers\subscription-db-updater.ts
  Line 1: import { SupabaseClient } from "@supabase/supabase-js";
  Line 2: import { SupabaseSubscriptionStatus } from "../types";
  Line 19: _supabase: SupabaseClient, // Original client not used, using admin client instead
  Line 21: status: SupabaseSubscriptionStatus,
  Line 91: adminClient: SupabaseClient,
  Line 93: status: SupabaseSubscriptionStatus,
  Line 173: const isValidStatus = status === SupabaseSubscriptionStatus._AUTHENTICATED ||
  Line 174: status === SupabaseSubscriptionStatus._ACTIVE;
  Line 183: if (status === SupabaseSubscriptionStatus._AUTHENTICATED) {

lib\razorpay\webhooks\handlers\subscriptionEventHandlers\handleSubscriptionActivated.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 25: _supabase: SupabaseClient,

lib\razorpay\webhooks\handlers\subscriptionEventHandlers\handleSubscriptionAuthenticated.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 3: import { SupabaseSubscriptionStatus } from "../../types";
  Line 36: _supabase: SupabaseClient,
  Line 80: const { data: existingSubscription, error: checkError } = await supabase
  Line 165: const { data: existingSubscriptions, error: findError } = await supabase
  Line 169: .eq('subscription_status', SupabaseSubscriptionStatus._AUTHENTICATED)
  Line 198: const { error: updateError } = await supabase
  Line 263: const { data: currentSubscriptionState, error: trialCheckError } = await supabase

lib\razorpay\webhooks\handlers\subscriptionEventHandlers\handleSubscriptionCancelled.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 24: _supabase: SupabaseClient,

lib\razorpay\webhooks\handlers\subscriptionEventHandlers\handleSubscriptionCharged.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 30: _supabase: SupabaseClient,

lib\razorpay\webhooks\handlers\subscriptionEventHandlers\handleSubscriptionCompleted.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 22: _supabase: SupabaseClient,

lib\razorpay\webhooks\handlers\subscriptionEventHandlers\handleSubscriptionExpired.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 23: _supabase: SupabaseClient,

lib\razorpay\webhooks\handlers\subscriptionEventHandlers\handleSubscriptionHalted.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 23: _supabase: SupabaseClient,

lib\razorpay\webhooks\handlers\subscriptionEventHandlers\handleSubscriptionPending.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 3: import { SupabaseSubscriptionStatus } from "../../types";
  Line 23: _supabase: SupabaseClient,
  Line 93: SupabaseSubscriptionStatus._PENDING,

lib\razorpay\webhooks\handlers\subscriptionEventHandlers\handleSubscriptionUpdated.ts
  Line 2: import { SupabaseClient } from "@supabase/supabase-js";
  Line 19: _supabase: SupabaseClient,

lib\razorpay\webhooks\handlers\webhookProcessor.ts
  Line 8: import { SupabaseClient } from "@supabase/supabase-js";
  Line 24: private adminClient: SupabaseClient | null;
  Line 30: private async getAdminClient(): Promise<SupabaseClient> {

lib\razorpay\webhooks\idempotency.ts
  Line 3: import { SupabaseClient } from "@supabase/supabase-js";
  Line 22: _supabase?: SupabaseClient // Not used, we use admin client
  Line 79: _supabase?: SupabaseClient // Renamed to _supabase to indicate it's not used
  Line 106: const { error } = await supabase
  Line 151: supabase?: SupabaseClient
  Line 156: const client = supabase || await createClient();

lib\razorpay\webhooks\monitoring.ts
  Line 34: const { data: statsData, error: statsError } = await supabase
  Line 57: const { data: timeData, error: timeError } = await supabase
  Line 109: const { data: inconsistencies, error } = await supabase
  Line 198: await supabase

lib\razorpay\webhooks\types.ts
  Line 63: export enum SupabaseSubscriptionStatus {
  Line 79: export function mapRazorpayStatusToSupabase(razorpayStatus: string): SupabaseSubscriptionStatus {
  Line 82: return SupabaseSubscriptionStatus._ACTIVE;
  Line 84: return SupabaseSubscriptionStatus._PENDING;
  Line 86: return SupabaseSubscriptionStatus._HALTED;
  Line 88: return SupabaseSubscriptionStatus._CANCELLED;
  Line 90: return SupabaseSubscriptionStatus._COMPLETED;
  Line 92: return SupabaseSubscriptionStatus._AUTHENTICATED;
  Line 94: return SupabaseSubscriptionStatus._EXPIRED;
  Line 98: return SupabaseSubscriptionStatus._PENDING;

lib\services\realtimeService.ts
  Line 7: } from "@supabase/supabase-js";
  Line 26: private supabase = createClient();
  Line 51: const channel = this.supabase
  Line 172: this.supabase.removeChannel(channel);
  Line 182: this.supabase.removeChannel(channel);

lib\services\subscription.ts
  Line 9: import { SupabaseClient } from "@supabase/supabase-js";
  Line 17: import { getPaymentSubscriptionByBusinessProfileId, getBusinessProfileById, getBusinessProfileSubscriptionInfo } from "@/lib/supabase/services/businessService";
  Line 46: private supabase: SupabaseClient | null = null;
  Line 48: private async getClient(): Promise<SupabaseClient> {
  Line 49: if (!this.supabase) {
  Line 50: this.supabase = await createClient();
  Line 52: return this.supabase;
  Line 64: const supabase = await this.getClient();
  Line 65: const { data: subscription, error: subError } = await getPaymentSubscriptionByBusinessProfileId(supabase, userId);
  Line 72: const { data: profile, error: profileError } = await getBusinessProfileSubscriptionInfo(supabase, userId);
  Line 150: const supabase = await this.getClient();
  Line 152: const { data: subscription, error: fetchError } = await supabase
  Line 163: const { data: subscriptionData, error: planFetchError } = await supabase
  Line 181: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
  Line 233: const supabase = await this.getClient();
  Line 234: const { data: subscriptions, error } = await supabase

lib\subscription\SubscriptionFlowTester.ts
  Line 10: import { SupabaseClient } from "@supabase/supabase-js";
  Line 35: private supabase: SupabaseClient | null = null;
  Line 44: private async getClient(): Promise<SupabaseClient> {
  Line 45: if (!this.supabase) {
  Line 46: this.supabase = await createClient();
  Line 48: return this.supabase;
  Line 249: const supabase = await this.getClient();
  Line 254: const { data: existingEvent } = await supabase
  Line 263: const { error: insertError } = await supabase
  Line 277: const { error: duplicateError } = await supabase
  Line 291: await supabase
  Line 324: const supabase = await this.getClient();
  Line 329: const { data: originalProfile } = await supabase
  Line 335: const { data: originalSubscription } = await supabase
  Line 342: await supabase
  Line 351: await supabase
  Line 361: const { data: validationResult, error: validationError } = await supabase
  Line 372: await supabase
  Line 383: await supabase

lib\testing\database.ts
  Line 8: import { SupabaseClient } from "@supabase/supabase-js";
  Line 9: import { Database } from "@/types/supabase";
  Line 13: private supabase: SupabaseClient<Database> | null = null;
  Line 15: private async getClient(): Promise<SupabaseClient<Database>> {
  Line 16: if (!this.supabase) {
  Line 17: this.supabase = await createClient();
  Line 19: return this.supabase;
  Line 26: const supabase = await this.getClient();
  Line 27: const { data: subscription } = await supabase
  Line 33: const { data: profile } = await supabase
  Line 57: const supabase = await this.getClient();
  Line 59: await supabase
  Line 72: await supabase
  Line 85: const supabase = await this.getClient();
  Line 87: await supabase
  Line 106: await supabase
  Line 116: await supabase
  Line 126: const supabase = await this.getClient();
  Line 127: const { data } = await supabase
  Line 140: const supabase = await this.getClient();
  Line 141: const { data } = await supabase
  Line 157: const supabase = await this.getClient();
  Line 158: const { data } = await supabase
  Line 176: const supabase = await this.getClient();
  Line 177: await supabase

lib\testing\testDataFactories.ts
  Line 6: import { AuthError, User } from '@supabase/supabase-js';
  Line 298: createSupabaseClient: () => ({

lib\testing\testUtils.ts
  Line 6: import { AuthError } from '@supabase/supabase-js';
  Line 88: export const createMockSupabaseClient = () => ({
  Line 224: export const mockSuccessfulOTPSend = (mockSupabase: { auth: { signInWithOtp: jest.Mock } }) => {
  Line 225: mockSupabase.auth.signInWithOtp.mockResolvedValue({
  Line 231: export const mockFailedOTPSend = (mockSupabase: { auth: { signInWithOtp: jest.Mock } }, error: AuthError) => {
  Line 232: mockSupabase.auth.signInWithOtp.mockResolvedValue({
  Line 238: export const mockSuccessfulOTPVerify = (mockSupabase: { auth: { verifyOtp: jest.Mock } }, user = createMockUser()) => {
  Line 239: mockSupabase.auth.verifyOtp.mockResolvedValue({
  Line 248: export const mockFailedOTPVerify = (mockSupabase: { auth: { verifyOtp: jest.Mock } }, error: AuthError) => {
  Line 249: mockSupabase.auth.verifyOtp.mockResolvedValue({
  Line 255: export const mockSuccessfulPasswordLogin = (mockSupabase: { auth: { signInWithPassword: jest.Mock } }, user = createMockUser()) => {
  Line 256: mockSupabase.auth.signInWithPassword.mockResolvedValue({
  Line 265: export const mockFailedPasswordLogin = (mockSupabase: { auth: { signInWithPassword: jest.Mock } }, error: AuthError) => {
  Line 266: mockSupabase.auth.signInWithPassword.mockResolvedValue({

lib\types\activities.ts
  Line 1: import { Tables } from "@/types/supabase";

lib\types\blog.ts
  Line 5: import { Tables } from "@/types/supabase";

lib\types\posts.ts
  Line 8: } from "@/types/supabase";

lib\utils\addressUtils.ts
  Line 23: const supabase = createClient();
  Line 27: let query = supabase
  Line 52: const { data: fallbackData, error: fallbackError } = await supabase

lib\utils\slugUtils.ts
  Line 5: import { checkBusinessSlugUniqueness } from "@/lib/supabase/services/businessService";
  Line 6: import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
  Line 41: const { user } = await getAuthenticatedUser(supabase);
  Line 45: const { available, error } = await checkBusinessSlugUniqueness(supabase, slug, currentUserId);

lib\utils\supabaseErrorHandler.ts
  Line 1: import { AuthError } from "@supabase/supabase-js";
  Line 80: export function handleSupabaseAuthError(error: AuthError | Error | null): string {

middleware.ts
  Line 2: import { updateSession } from "./utils/supabase/middleware";

next.config.ts
  Line 10: hostname: "rnjolcoecogzgglnblqn.supabase.co",

package-lock.json
  Line 35: "@supabase/ssr": "^0.6.1",
  Line 36: "@supabase/supabase-js": "^2.50.0",
  Line 102: "supabase": "^2.31.4",
  Line 4414: "node_modules/@supabase/auth-js": {
  Line 4416: "resolved": "https://registry.npmjs.org/@supabase/auth-js/-/auth-js-2.70.0.tgz",
  Line 4420: "@supabase/node-fetch": "^2.6.14"
  Line 4423: "node_modules/@supabase/functions-js": {
  Line 4425: "resolved": "https://registry.npmjs.org/@supabase/functions-js/-/functions-js-2.4.4.tgz",
  Line 4429: "@supabase/node-fetch": "^2.6.14"
  Line 4432: "node_modules/@supabase/node-fetch": {
  Line 4434: "resolved": "https://registry.npmjs.org/@supabase/node-fetch/-/node-fetch-2.6.15.tgz",
  Line 4444: "node_modules/@supabase/postgrest-js": {
  Line 4446: "resolved": "https://registry.npmjs.org/@supabase/postgrest-js/-/postgrest-js-1.19.4.tgz",
  Line 4450: "@supabase/node-fetch": "^2.6.14"
  Line 4453: "node_modules/@supabase/realtime-js": {
  Line 4455: "resolved": "https://registry.npmjs.org/@supabase/realtime-js/-/realtime-js-2.11.10.tgz",
  Line 4459: "@supabase/node-fetch": "^2.6.13",
  Line 4465: "node_modules/@supabase/ssr": {
  Line 4467: "resolved": "https://registry.npmjs.org/@supabase/ssr/-/ssr-0.6.1.tgz",
  Line 4474: "@supabase/supabase-js": "^2.43.4"
  Line 4477: "node_modules/@supabase/storage-js": {
  Line 4479: "resolved": "https://registry.npmjs.org/@supabase/storage-js/-/storage-js-2.7.1.tgz",
  Line 4483: "@supabase/node-fetch": "^2.6.14"
  Line 4486: "node_modules/@supabase/supabase-js": {
  Line 4488: "resolved": "https://registry.npmjs.org/@supabase/supabase-js/-/supabase-js-2.50.0.tgz",
  Line 4492: "@supabase/auth-js": "2.70.0",
  Line 4493: "@supabase/functions-js": "2.4.4",
  Line 4494: "@supabase/node-fetch": "2.6.15",
  Line 4495: "@supabase/postgrest-js": "1.19.4",
  Line 4496: "@supabase/realtime-js": "2.11.10",
  Line 4497: "@supabase/storage-js": "2.7.1"
  Line 15682: "node_modules/supabase": {
  Line 15684: "resolved": "https://registry.npmjs.org/supabase/-/supabase-2.31.4.tgz",
  Line 15696: "supabase": "bin/supabase"
  Line 15702: "node_modules/supabase/node_modules/agent-base": {
  Line 15712: "node_modules/supabase/node_modules/https-proxy-agent": {

package.json
  Line 12: "gen-types": "npx supabase gen types typescript --schema public > types/supabase.ts"
  Line 39: "@supabase/ssr": "^0.6.1",
  Line 40: "@supabase/supabase-js": "^2.50.0",
  Line 106: "supabase": "^2.31.4",

README.md
  Line 15: For backend services, DukanCard integrates with **Supabase**, which provides a comprehensive suite of tools including authentication, PostgreSQL database management, and real-time capabilities. Payment processing is handled via **Razorpay**, enabling secure subscription management and transaction history.
  Line 25: ### Supabase Type Generation
  Line 27: To ensure your local environment has the latest Supabase TypeScript types, run the following command from the project root:
  Line 33: This command generates `types/supabase.ts` based on your Supabase schema.

scripts\test-webhook-handlers.ts
  Line 33: const { createClient } = await import('../utils/supabase/server');
  Line 42: await supabase
  Line 53: await supabase
  Line 62: const { data: initialSub } = await supabase
  Line 68: const { data: initialProfile } = await supabase
  Line 101: const result = await handleSubscriptionCancelled(payload as unknown as RazorpayWebhookData, supabase, `test_free_${Date.now()}`);
  Line 106: const { data: finalSub } = await supabase
  Line 112: const { data: finalProfile } = await supabase
  Line 163: const { createClient } = await import('../utils/supabase/server');
  Line 173: await supabase
  Line 207: const firstResult = await handleSubscriptionCancelled(payload as unknown as RazorpayWebhookData, supabase, eventId);
  Line 211: const { data: firstState } = await supabase
  Line 218: const secondResult = await handleSubscriptionCancelled(payload as unknown as RazorpayWebhookData, supabase, eventId);
  Line 222: const { data: secondState } = await supabase
  Line 265: const { createClient } = await import('../utils/supabase/server');
  Line 275: await supabase
  Line 312: handleSubscriptionCancelled(payload as unknown as RazorpayWebhookData, supabase, `test_race_${Date.now()}_${index}`)
  Line 319: const { data: finalState } = await supabase

scripts\webhook-testing\payment-method-handling.md
  Line 97: await supabase.rpc('update_subscription_atomic', {
  Line 130: await supabase.from('payment_subscriptions').insert({
  Line 266: await supabase.rpc('update_subscription_atomic', {

scripts\webhook-testing\production-stress-test.ts
  Line 39: await runner.initialize(); // Initialize the Supabase client

scripts\webhook-testing\README.md
  Line 169: NEXT_PUBLIC_SUPABASE_URL=https://rnjolcoecogzgglnblqn.supabase.co
  Line 170: SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
  Line 442: 4. **Network Issues**: Verify Supabase and Razorpay connectivity

scripts\webhook-testing\real-world-user-journeys.ts
  Line 215: await runner.initialize(); // Initialize the Supabase client

scripts\webhook-testing\runners\scenarioTestRunner.ts
  Line 32: private supabase: any;
  Line 42: const { createClient } = await import('../../../utils/supabase/server');
  Line 43: this.supabase = createClient();
  Line 164: await this.supabase
  Line 179: await this.supabase
  Line 204: await this.supabase
  Line 227: await this.supabase
  Line 248: await this.supabase
  Line 274: await this.supabase
  Line 321: await this.supabase
  Line 456: const { data: subscription } = await this.supabase
  Line 462: const { data: profile } = await this.supabase
  Line 604: const { data: existingSubscription } = await this.supabase
  Line 641: const { data, error } = await this.supabase

scripts\webhook-testing\subscription-statuses.md
  Line 225: const result = await supabase.rpc('update_subscription_atomic', {

scripts\webhook-testing\test-webhook-events.ts
  Line 32: const { createClient } = await import('../../utils/supabase/server');
  Line 33: const supabase = createClient();
  Line 64: await setupTestState(supabase, event.eventType);
  Line 74: const result = await (handler as any)(payload, supabase, eventId);
  Line 162: async function setupTestState(supabase: any, eventType: string) {
  Line 166: await supabase
  Line 176: await supabase

types\gallery.ts
  Line 1: import { Json } from "../types/supabase";

types\products.ts
  Line 4: import { Tables, TablesInsert, TablesUpdate } from "./supabase";

types\supabase.ts
  Line 12: __InternalSupabase: {
  Line 1745: type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

types\variants.ts
  Line 3: import { Tables, Json } from "./supabase";

utils\business-validation.ts
  Line 4: import { type Tables } from "@/types/supabase";

utils\supabase\client.ts
  Line 1: import { createBrowserClient } from '@supabase/ssr';
  Line 2: import { Database } from '@/types/supabase';
  Line 5: const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  Line 6: const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  Line 8: if (!supabaseUrl || !supabaseAnonKey) {
  Line 9: console.error("Supabase environment variables are not set. Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are defined.");
  Line 14: supabaseUrl,
  Line 15: supabaseAnonKey

utils\supabase\middleware.ts
  Line 1: import { createServerClient } from "@supabase/ssr";
  Line 5: let supabaseResponse = NextResponse.next({
  Line 9: const supabase = createServerClient(
  Line 10: process.env.NEXT_PUBLIC_SUPABASE_URL!,
  Line 11: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  Line 21: supabaseResponse = NextResponse.next({
  Line 25: supabaseResponse.cookies.set(name, value, options)
  Line 38: } = await supabase.auth.getUser();
  Line 65: supabase
  Line 70: supabase
  Line 120: const { data: subscriptionData } = await supabase
  Line 220: return supabaseResponse;

utils\supabase\server.ts
  Line 1: import { createServerClient, type CookieOptions } from '@supabase/ssr';
  Line 2: import { SupabaseClient } from '@supabase/supabase-js';
  Line 4: import { Database } from '@/types/supabase';
  Line 6: export async function createClient(): Promise<SupabaseClient<Database>> {
  Line 7: const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  Line 8: const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  Line 10: if (!supabaseUrl || !supabaseAnonKey) {
  Line 11: throw new Error('Supabase environment variables are not set.');
  Line 23: return createMockSupabaseClient(headersList) as unknown as SupabaseClient<Database>;
  Line 29: supabaseUrl,
  Line 30: supabaseAnonKey,
  Line 49: ) as unknown as SupabaseClient<Database>;
  Line 91: function createMockSupabaseClient(headersList: Headers) {

utils\supabase\sitemap.ts
  Line 1: import { createClient as createSupabaseClient } from "@supabase/supabase-js";
  Line 9: return createSupabaseClient(
  Line 10: process.env.NEXT_PUBLIC_SUPABASE_URL!,
  Line 11: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
