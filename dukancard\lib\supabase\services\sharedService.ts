import { SupabaseClient, RealtimePostgresChangesPayload } from "@supabase/supabase-js";
import { TABLES, COLUMNS } from "../constants";
import { Tables } from "../../../types/supabase";

/**
 * Fetches the currently authenticated user.
 * @param supabase The Supabase client.
 * @returns An object containing the user data or an error.
 */
export async function getAuthenticatedUser(supabase: SupabaseClient) {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
      console.error(`Error fetching authenticated user: ${error.message}`);
      return { user: null, error: "User not found or authentication error." };
    }
    return { user, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching authenticated user: ${err}`);
    return { user: null, error: "An unexpected error occurred." };
  }
}

/**
 * Uploads a file to Supabase Storage.
 * @param supabase The Supabase client.
 * @param bucketName The name of the storage bucket.
 * @param path The path where the file will be stored in the bucket.
 * @param fileBuffer The file content as a Buffer.
 * @param contentType The content type of the file (e.g., 'image/jpeg').
 * @param upsert Whether to upsert the file if it already exists.
 * @returns An object indicating success or an error.
 */
export async function uploadFileToStorage(
  supabase: SupabaseClient,
  bucketName: string,
  path: string,
  fileBuffer: Buffer,
  contentType: string,
  upsert: boolean = true
) {
  try {
    const { error } = await supabase.storage
      .from(bucketName)
      .upload(path, fileBuffer, {
        contentType,
        upsert,
      });

    if (error) {
      console.error(`Error uploading file to storage: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error uploading file to storage: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Retrieves the public URL for a file in Supabase Storage.
 * @param supabase The Supabase client.
 * @param bucketName The name of the storage bucket.
 * @param path The path of the file in the bucket.
 * @returns An object containing the public URL or an error.
 */
export async function getPublicUrlFromStorage(supabase: SupabaseClient, bucketName: string, path: string) {
  try {
    const { data } = supabase.storage.from(bucketName).getPublicUrl(path);
    if (!data?.publicUrl) {
      return { publicUrl: null, error: "Could not retrieve public URL." };
    }
    return { publicUrl: data.publicUrl, error: null };
  } catch (err) {
    console.error(`Unexpected error getting public URL: ${err}`);
    return { publicUrl: null, error: "An unexpected error occurred." };
  }
}

/**
 * Removes files from Supabase Storage.
 * @param supabase The Supabase client.
 * @param bucketName The name of the storage bucket.
 * @param paths An array of file paths to remove from the bucket.
 * @returns An object indicating success or an error.
 */
export async function removeFileFromStorage(supabase: SupabaseClient, bucketName: string, paths: string[]) {
  try {
    const { error } = await supabase.storage.from(bucketName).remove(paths);
    if (error) {
      console.error(`Error removing file from storage: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error removing file from storage: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Updates the authenticated user's phone number in Supabase Auth.
 * @param supabase The Supabase client.
 * @param phone The new phone number.
 * @returns An object indicating success or an error.
 */
export async function updateAuthUserPhone(supabase: SupabaseClient, phone: string) {
  try {
    const { error } = await supabase.auth.updateUser({
      phone: `+91${phone}`,
    });

    if (error) {
      console.error(`Error updating auth user phone: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error updating auth user phone: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Lists files in a Supabase Storage bucket.
 * @param supabase The Supabase client.
 * @param bucketName The name of the storage bucket.
 * @param path The path within the bucket to list files from.
 * @param options Options for listing files (e.g., limit).
 * @returns An object containing the list of files or an error.
 */
export async function listStorageFiles(
  supabase: SupabaseClient,
  bucketName: string,
  path: string,
  options?: { limit?: number; offset?: number; search?: string }
) {
  try {
    const { data, error } = await supabase.storage
      .from(bucketName)
      .list(path, options);

    if (error) {
      console.error(`Error listing storage files: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error listing storage files: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Signs out the currently authenticated user.
 * @param supabase The Supabase client.
 * @returns An object indicating success or an error.
 */
export async function signOutUser(supabase: SupabaseClient) {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error(`Error signing out user: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error signing out user: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Sends an OTP to the provided email address for authentication.
 * @param supabase The Supabase client.
 * @param email The email address to send the OTP to.
 * @param shouldCreateUser Whether to create a new user if the email doesn't exist.
 * @returns An object indicating success or an error.
 */
export async function signInWithOtp(supabase: SupabaseClient, email: string, shouldCreateUser: boolean = true) {
  try {
    const { error } = await supabase.auth.signInWithOtp({
      email: email,
      options: {
        shouldCreateUser: shouldCreateUser,
        data: {
          auth_type: "email",
        },
      },
    });
    if (error) {
      console.error(`Error sending OTP: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error sending OTP: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Verifies an OTP for email authentication.
 * @param supabase The Supabase client.
 * @param email The email address associated with the OTP.
 * @param token The OTP token to verify.
 * @returns An object containing user data on success or an error.
 */
export async function verifyOtp(supabase: SupabaseClient, email: string, token: string) {
  try {
    const { data, error } = await supabase.auth.verifyOtp({
      email: email,
      token: token,
      type: 'email',
    });
    if (error) {
      console.error(`Error verifying OTP: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error verifying OTP: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Signs in a user with a mobile number and password.
 * @param supabase The Supabase client.
 * @param phone The user's phone number (should include country code, e.g., +91).
 * @param password The user's password.
 * @returns An object containing user data on success or an error.
 */
export async function signInWithPassword(supabase: SupabaseClient, phone: string, password: string) {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      phone: phone,
      password: password,
    });
    if (error) {
      console.error(`Error signing in with password: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error signing in with password: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Initiates an OAuth sign-in flow with a specified provider.
 * @param supabase The Supabase client.
 * @param provider The OAuth provider (e.g., 'google').
 * @param redirectTo The URL to redirect to after successful authentication.
 * @param queryParams Optional query parameters for the OAuth flow.
 * @returns An object containing the authorization URL or an error.
 */
export async function signInWithOAuth(supabase: SupabaseClient, provider: "google", redirectTo: string, queryParams?: { [key: string]: string }) {
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: provider,
      options: {
        redirectTo: redirectTo,
        skipBrowserRedirect: true,
        queryParams: queryParams,
      },
    });
    if (error) {
      console.error(`Error initiating OAuth sign-in: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error initiating OAuth sign-in: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Subscribes to real-time changes in a Supabase table.
 * @param supabase The Supabase client.
 * @param tableName The name of the table to subscribe to.
 * @param filter The filter to apply to the subscription (e.g., `id=eq.some_id`).
 * @param callback The callback function to execute when changes are received.
 * @returns A cleanup function to unsubscribe from the channel.
 */
export function subscribeToTableChanges<T extends { [key: string]: any }>(
  supabase: SupabaseClient,
  tableName: string,
  filter: string,
  callback: (_payload: RealtimePostgresChangesPayload<T>) => void
): () => void {

  const channel = supabase
    .channel(`public:${tableName}`)
    .on<T>(
      "postgres_changes",
      {
        event: "*",
        schema: "public",
        table: tableName,
        filter: filter,
      },
      callback
    )
    .subscribe();

  return () => {
    supabase.removeChannel(channel);
  };
}

/**
 * Fetches user subscriptions.
 * @param supabase The Supabase client.
 * @param userId The ID of the user.
 * @returns An object containing the subscriptions data or an error.
 */
export async function fetchUserSubscriptions(supabase: SupabaseClient, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.SUBSCRIPTIONS)
      .select(COLUMNS.BUSINESS_PROFILE_ID)
      .eq(COLUMNS.USER_ID, userId);

    if (error) {
      console.error(`Error fetching user subscriptions: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching user subscriptions: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the state name for a given city from the pincodes table.
 * @param supabase The Supabase client.
 * @param city The city name to search for.
 * @returns An object containing the state name or an error.
 */
export async function getStateNameByCity(supabase: SupabaseClient, city: string): Promise<{ stateName: string | null; error: string | null }> {
  try {
    const { data, error } = await supabase
      .from(TABLES.PINCODES)
      .select(COLUMNS.STATE_NAME)
      .ilike(COLUMNS.DIVISION_NAME, `%${city}%`)
      .limit(1);

    if (error) {
      console.error(`Error fetching state name for city ${city}: ${error.message}`);
      return { stateName: null, error: error.message };
    }

    return { stateName: data && data.length > 0 ? data[0].StateName : null, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching state name for city ${city}: ${err}`);
    return { stateName: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches unified posts with optional filters and pagination.
 * @param supabase The Supabase client.
 * @param from The starting index for pagination.
 * @param to The ending index for pagination.
 * @param conditions Optional array of conditions for filtering.
 * @returns An object containing the unified posts data, count, or an error.
 */
export async function getUnifiedPosts(
  supabase: SupabaseClient,
  from: number,
  to: number,
  conditions: string[] = []
) {
  let query = supabase.from(TABLES.UNIFIED_POSTS).select('*', { count: 'exact' });

  if (conditions.length > 0) {
    query = query.or(conditions.join(','));
  }

  const { data, error, count } = await query
    .order(COLUMNS.CREATED_AT, { ascending: false })
    .range(from, to);

  if (error) {
    console.error("Error fetching unified posts:", error);
    return { data: null, error: error.message, count: null };
  }
  return { data, error: null, count };
}

/**
 * Fetches address data from the pincodes table.
 * @param supabase The Supabase client.
 * @param pincode The pincode to search for.
 * @param locality_slug Optional. The locality slug to filter by.
 * @param city_slug Optional. The city slug to filter by.
 * @param state_slug Optional. The state slug to filter by.
 * @returns An object containing the address data or an error.
 */
export async function fetchPincodeAddress(
  supabase: SupabaseClient,
  pincode: string,
  locality_slug?: string | null,
  city_slug?: string | null,
  state_slug?: string | null
) {
  try {
    let query = supabase
      .from(TABLES.PINCODES)
      .select(`${COLUMNS.OFFICE_NAME}, ${COLUMNS.DIVISION_NAME}, ${COLUMNS.STATE_NAME}, ${COLUMNS.PINCODE}`);

    if (pincode) {
      query = query.eq(COLUMNS.PINCODE, pincode);
    }
    if (city_slug) {
      query = query.eq(COLUMNS.CITY_SLUG, city_slug);
    }
    if (state_slug) {
      query = query.eq(COLUMNS.STATE_SLUG, state_slug);
    }
    if (locality_slug) {
      query = query.eq(COLUMNS.LOCALITY_SLUG, locality_slug);
    }

    const { data, error } = await query.limit(1);

    if (error) {
      console.error(`Error fetching pincode address: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data: data[0] || null, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching pincode address: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Generic function to fetch profile location data from any profile table.
 * @param supabase The Supabase client.
 * @param tableName The name of the profile table.
 * @param userId The ID of the user.
 * @returns An object containing the location data or an error.
 */
export async function getProfileLocation(supabase: SupabaseClient, tableName: string, userId: string) {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select(`${COLUMNS.LOCALITY_SLUG}, ${COLUMNS.PINCODE}, ${COLUMNS.CITY_SLUG}, ${COLUMNS.STATE_SLUG}`)
      .eq(COLUMNS.ID, userId)
      .single();

    if (error) {
      console.error(`Error fetching profile location from ${tableName}: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching profile location from ${tableName}: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Generic function to check if a profile exists in any profile table.
 * @param supabase The Supabase client.
 * @param tableName The name of the profile table.
 * @param userId The ID of the user to check.
 * @returns An object containing a boolean indicating if the profile exists and an error if one occurred.
 */
export async function checkProfileExists(supabase: SupabaseClient, tableName: string, userId: string) {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select(COLUMNS.ID)
      .eq(COLUMNS.ID, userId)
      .maybeSingle();

    if (error) {
      console.error(`Error checking existing profile in ${tableName}: ${error.message}`);
      return { exists: false, error: `Database error checking profile in ${tableName}.` };
    }

    return { exists: !!data, error: null };
  } catch (err) {
    console.error(`Unexpected error checking profile in ${tableName}: ${err}`);
    return { exists: false, error: "An unexpected error occurred." };
  }
}

/**
 * Generic function to call Supabase RPC functions with consistent error handling.
 * @param supabase The Supabase client.
 * @param functionName The name of the RPC function to call.
 * @param params The parameters to pass to the RPC function.
 * @returns An object containing the RPC result data or an error.
 */
export async function callRpcFunction(supabase: SupabaseClient, functionName: string, params: Record<string, unknown>) {
  try {
    const { data, error } = await supabase.rpc(functionName, params);
    if (error) {
      console.error(`Error calling RPC function ${functionName}: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error calling RPC function ${functionName}: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}